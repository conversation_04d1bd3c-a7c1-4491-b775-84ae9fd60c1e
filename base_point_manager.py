"""
基准点管理模块
负责基准点模板提取、匹配和ROI动态对齐功能
"""

import cv2
import numpy as np
import logging
from typing import Tuple, List, Optional
from app_state import AppState
from constants import *


def _is_base_point_config_complete(app_state: AppState) -> bool:
    """
    安全地检查基准点配置是否完整

    Args:
        app_state: 应用状态对象

    Returns:
        配置完整性标志
    """
    try:
        # 检查原始基准点坐标
        if (app_state.original_base_points[0] is None or
            app_state.original_base_points[1] is None):
            return False

        # 检查基准点模板
        if (app_state.base_templates[0] is None or
            app_state.base_templates[1] is None):
            return False

        return True
    except (IndexError, AttributeError):
        return False


def extract_base_template(frame: np.ndarray, center_x: int, center_y: int,
                         template_size: int = BASE_TEMPLATE_SIZE) -> Optional[np.ndarray]:
    """
    从指定中心点提取基准点模板图像

    Args:
        frame: 输入图像帧
        center_x: 基准点中心X坐标
        center_y: 基准点中心Y坐标
        template_size: 模板尺寸，默认30x30像素

    Returns:
        提取的模板图像，如果提取失败返回None
    """
    try:
        if frame is None or frame.size == 0:
            logging.warning("extract_base_template: 输入帧无效")
            return None

        # 参数验证
        if not isinstance(center_x, int) or not isinstance(center_y, int):
            logging.error(f"extract_base_template: 坐标参数类型错误 center_x={type(center_x)}, center_y={type(center_y)}")
            return None

        if template_size <= 0 or template_size > 200:
            logging.error(f"extract_base_template: 模板尺寸参数异常 template_size={template_size}")
            return None

        half_size = template_size // 2
        frame_h, frame_w = frame.shape[:2]

        # 检查坐标是否在图像范围内
        if center_x < 0 or center_x >= frame_w or center_y < 0 or center_y >= frame_h:
            logging.warning(f"extract_base_template: 中心点坐标超出图像范围 ({center_x},{center_y}), 图像尺寸=({frame_w},{frame_h})")
            return None

        # 计算模板边界
        x1 = max(0, center_x - half_size)
        y1 = max(0, center_y - half_size)
        x2 = min(frame_w, center_x + half_size)
        y2 = min(frame_h, center_y + half_size)

        # 检查边界有效性
        if x2 <= x1 or y2 <= y1:
            logging.warning(f"extract_base_template: 模板边界无效 ({x1},{y1}) to ({x2},{y2})")
            return None

        # 检查模板尺寸是否足够
        actual_w, actual_h = x2 - x1, y2 - y1
        min_size = template_size * 0.8
        if actual_w < min_size or actual_h < min_size:
            logging.warning(f"extract_base_template: 模板尺寸不足 {actual_w}x{actual_h}, 期望至少 {min_size:.0f}x{min_size:.0f}")
            return None

        # 提取模板
        template = frame[y1:y2, x1:x2].copy()

        if template.size == 0:
            logging.error("extract_base_template: 提取的模板为空")
            return None

        # 验证模板质量（检查对比度）
        try:
            if len(template.shape) == 3:
                gray_template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                gray_template = template

            contrast = cv2.Laplacian(gray_template, cv2.CV_64F).var()

            if contrast < 50:
                logging.warning(f"extract_base_template: 基准点对比度很低 ({contrast:.1f}), 强烈建议重新选择特征更明显的点")
                return None
            elif contrast < 100:
                logging.warning(f"extract_base_template: 基准点对比度较低 ({contrast:.1f}), 可能影响匹配精度")

            logging.debug(f"extract_base_template: 成功提取模板 {actual_w}x{actual_h}, 对比度: {contrast:.1f}")
            return template

        except Exception as e:
            logging.error(f"extract_base_template: 模板质量检查失败: {e}")
            return template  # 即使质量检查失败，也返回模板

    except Exception as e:
        logging.error(f"extract_base_template: 发生未预期错误: {e}")
        return None


def find_base_points(frame: np.ndarray, app_state: AppState) -> Tuple[bool, List[Tuple[int, int]]]:
    """
    在当前帧中搜索基准点位置
    
    Args:
        frame: 输入图像帧
        app_state: 应用状态对象
        
    Returns:
        (成功标志, 基准点坐标列表)
    """
    if frame is None or frame.size == 0:
        logging.warning("find_base_points: 输入帧无效")
        return False, []
    
    if app_state.base_templates[0] is None or app_state.base_templates[1] is None:
        logging.debug("find_base_points: 基准点模板未设置")
        return False, []

    if app_state.original_base_points[0] is None or app_state.original_base_points[1] is None:
        logging.warning("find_base_points: 原始基准点位置未设置")
        return False, []
    
    current_points = []
    match_scores = []
    
    # 对两个基准点分别进行模板匹配
    for i in range(2):
        template = app_state.base_templates[i]
        
        try:
            # 模板匹配
            result = cv2.matchTemplate(frame, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= app_state.base_match_threshold:
                # 计算基准点中心坐标
                center_x = max_loc[0] + app_state.base_template_size // 2
                center_y = max_loc[1] + app_state.base_template_size // 2
                current_points.append((center_x, center_y))
                match_scores.append(max_val)
                logging.debug(f"find_base_points: 基准点{i+1} 匹配成功, 位置=({center_x},{center_y}), 分数={max_val:.3f}")
            else:
                logging.debug(f"find_base_points: 基准点{i+1} 匹配失败, 分数={max_val:.3f} < 阈值{app_state.base_match_threshold}")
                return False, []
                
        except Exception as e:
            logging.error(f"find_base_points: 基准点{i+1} 匹配时出错: {e}")
            return False, []
    
    # 验证基准点间距离是否合理（避免误匹配）
    if len(current_points) == 2:
        current_dist = np.sqrt((current_points[0][0] - current_points[1][0])**2 + 
                              (current_points[0][1] - current_points[1][1])**2)
        original_dist = np.sqrt((app_state.original_base_points[0][0] - app_state.original_base_points[1][0])**2 +
                               (app_state.original_base_points[0][1] - app_state.original_base_points[1][1])**2)
        
        # 距离变化超过20%认为异常
        distance_change_ratio = abs(current_dist - original_dist) / original_dist if original_dist > 0 else 1.0
        if distance_change_ratio > 0.2:
            logging.warning(f"find_base_points: 基准点距离变化异常 {distance_change_ratio:.2%}, "
                          f"当前距离={current_dist:.1f}, 原始距离={original_dist:.1f}")
            return False, []
    
    logging.debug(f"find_base_points: 成功找到基准点, 匹配分数=[{match_scores[0]:.3f}, {match_scores[1]:.3f}]")
    return True, current_points


def adjust_all_rois(app_state: AppState, dx: int, dy: int) -> None:
    """
    批量调整所有ROI坐标
    
    Args:
        app_state: 应用状态对象
        dx: X方向偏移量
        dy: Y方向偏移量
    """
    adjusted_count = 0
    
    # 调整LED ROI
    for i in range(min(app_state.led_max_rois, len(app_state.original_led_rois))):
        if app_state.original_led_rois[i] is not None:
            ox, oy, ow, oh = app_state.original_led_rois[i]
            app_state.led_rois[i] = (ox + dx, oy + dy, ow, oh)
            adjusted_count += 1

    # 调整数码管整体ROI
    for i in range(min(NUM_DIGITS, len(app_state.original_digit_rois))):
        if app_state.original_digit_rois[i] is not None:
            ox, oy, ow, oh = app_state.original_digit_rois[i]
            app_state.digit_rois[i] = (ox + dx, oy + dy, ow, oh)
            adjusted_count += 1

    # 调整数码管段ROI
    for i in range(min(NUM_DIGITS, len(app_state.original_digit_segment_rois))):
        for j in range(min(NUM_SEGMENTS_PER_DIGIT, len(app_state.original_digit_segment_rois[i]))):
            if app_state.original_digit_segment_rois[i][j] is not None:
                ox, oy, ow, oh = app_state.original_digit_segment_rois[i][j]
                app_state.digit_segment_rois[i][j] = (ox + dx, oy + dy, ow, oh)
                adjusted_count += 1
    
    logging.debug(f"adjust_all_rois: 调整了 {adjusted_count} 个ROI, 偏移量=({dx}, {dy})")


def auto_align_rois(app_state: AppState, frame: np.ndarray) -> bool:
    """
    自动对齐所有ROI到当前产品位置

    Args:
        app_state: 应用状态对象
        frame: 当前图像帧

    Returns:
        对齐成功标志
    """
    try:
        if not app_state.alignment_enabled:
            return True  # 禁用时直接返回成功

        # 验证基准点配置完整性
        if not _is_base_point_config_complete(app_state):
            if app_state.alignment_fail_count == 0:  # 只记录一次
                logging.warning("auto_align_rois: 基准点配置不完整，跳过对齐")
            app_state.alignment_fail_count += 1
            return False

        # 搜索基准点
        success, current_points = find_base_points(frame, app_state)

        if not success:
            app_state.alignment_fail_count += 1
            app_state.last_alignment_success = False

            # 分级日志记录
            if app_state.alignment_fail_count == 1:
                logging.info("基准点对齐失败，开始使用固定坐标")
            elif app_state.alignment_fail_count == BASE_ALIGNMENT_TIMEOUT:
                logging.warning(f"基准点对齐连续失败{app_state.alignment_fail_count}次，建议检查产品位置或光照条件")
            elif app_state.alignment_fail_count % 10 == 0:  # 每10次失败记录一次
                logging.warning(f"基准点对齐持续失败{app_state.alignment_fail_count}次")

            return False

        # 重置失败计数
        if app_state.alignment_fail_count > 0:
            logging.info(f"基准点对齐恢复成功（之前失败{app_state.alignment_fail_count}次）")
        app_state.alignment_fail_count = 0
        app_state.last_alignment_success = True

        # 计算平移偏移量（使用平均偏移）
        dx1 = current_points[0][0] - app_state.original_base_points[0][0]
        dy1 = current_points[0][1] - app_state.original_base_points[0][1]
        dx2 = current_points[1][0] - app_state.original_base_points[1][0]
        dy2 = current_points[1][1] - app_state.original_base_points[1][1]

        # 使用平均偏移（简单平移模型）
        dx = int(round((dx1 + dx2) / 2.0))
        dy = int(round((dy1 + dy2) / 2.0))

        # 检测异常偏移
        max_offset = max(abs(dx), abs(dy))
        if max_offset > 100:  # 偏移超过100像素可能异常
            logging.warning(f"auto_align_rois: 检测到异常偏移量=({dx}, {dy}), 可能存在误匹配")

        logging.debug(f"auto_align_rois: 偏移量=({dx}, {dy}), 基准点偏移=[({dx1},{dy1}), ({dx2},{dy2})]")

        # 调整所有ROI
        adjust_all_rois(app_state, dx, dy)

        # 更新当前基准点位置
        app_state.base_points = current_points.copy()

        return True

    except Exception as e:
        import traceback
        logging.error(f"auto_align_rois: 发生未预期错误: {e}")
        logging.error(f"错误详情: {traceback.format_exc()}")
        app_state.alignment_fail_count += 1
        app_state.last_alignment_success = False
        return False


def save_original_roi_coordinates(app_state: AppState) -> None:
    """
    保存当前ROI坐标作为原始坐标（用于后续偏移计算）
    
    Args:
        app_state: 应用状态对象
    """
    # 保存LED ROI原始坐标
    app_state.original_led_rois = [tuple(roi) if roi is not None else None for roi in app_state.led_rois]

    # 保存数码管整体ROI原始坐标
    app_state.original_digit_rois = [tuple(roi) if roi is not None else None for roi in app_state.digit_rois]

    # 保存数码管段ROI原始坐标
    for i in range(NUM_DIGITS):
        for j in range(NUM_SEGMENTS_PER_DIGIT):
            if app_state.digit_segment_rois[i][j] is not None:
                app_state.original_digit_segment_rois[i][j] = tuple(app_state.digit_segment_rois[i][j])
    
    logging.info("save_original_roi_coordinates: 已保存所有ROI的原始坐标")


def reset_alignment_system(app_state: AppState) -> None:
    """
    重置基准点对齐系统

    Args:
        app_state: 应用状态对象
    """
    app_state.base_points = [None, None]
    app_state.base_templates = [None, None]
    app_state.original_base_points = [None, None]
    app_state.alignment_fail_count = 0
    app_state.last_alignment_success = False
    app_state.calib_base_point_index = 0

    logging.info("reset_alignment_system: 基准点对齐系统已重置")


def validate_alignment_system(app_state: AppState) -> Tuple[bool, List[str]]:
    """
    验证基准点对齐系统的完整性和健康状态

    Args:
        app_state: 应用状态对象

    Returns:
        (系统健康标志, 问题列表)
    """
    issues = []

    try:
        # 检查基准点对齐是否启用
        if not app_state.alignment_enabled:
            return True, ["基准点对齐功能已禁用"]

        # 检查基准点坐标
        if (app_state.original_base_points[0] is None or
            app_state.original_base_points[1] is None):
            issues.append("原始基准点坐标未设置")

        # 检查基准点模板
        if (app_state.base_templates[0] is None or
            app_state.base_templates[1] is None):
            issues.append("基准点模板未设置")
        else:
            # 检查模板质量
            for i, template in enumerate(app_state.base_templates):
                if template is not None:
                    h, w = template.shape[:2]
                    if w < 20 or h < 20:
                        issues.append(f"基准点模板{i+1}尺寸过小({w}x{h})")
                    elif w > 100 or h > 100:
                        issues.append(f"基准点模板{i+1}尺寸过大({w}x{h})")

        # 检查基准点距离
        if (app_state.original_base_points[0] is not None and
            app_state.original_base_points[1] is not None):
            p1, p2 = app_state.original_base_points
            distance = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
            if distance < 50:
                issues.append(f"基准点距离过近({distance:.1f}像素)，建议>50像素")
            elif distance > 500:
                issues.append(f"基准点距离过远({distance:.1f}像素)，可能影响精度")

        # 检查匹配参数
        if app_state.base_match_threshold < 0.5:
            issues.append(f"匹配阈值过低({app_state.base_match_threshold})")
        elif app_state.base_match_threshold > 0.95:
            issues.append(f"匹配阈值过高({app_state.base_match_threshold})")

        # 检查连续失败次数
        if app_state.alignment_fail_count > BASE_ALIGNMENT_TIMEOUT:
            issues.append(f"基准点对齐连续失败{app_state.alignment_fail_count}次")

        # 检查原始ROI数据完整性
        roi_count = 0
        # 安全地检查LED ROI
        led_roi_count = sum(1 for roi in app_state.original_led_rois if roi is not None)
        roi_count += led_roi_count

        # 安全地检查数码管ROI
        digit_roi_count = sum(1 for roi in app_state.original_digit_rois if roi is not None)
        roi_count += digit_roi_count

        if roi_count == 0:
            issues.append("没有原始ROI数据，基准点对齐无意义")

        is_healthy = len(issues) == 0
        return is_healthy, issues

    except Exception as e:
        logging.error(f"validate_alignment_system: 验证过程异常: {e}")
        return False, [f"系统验证异常: {e}"]


def get_alignment_status_summary(app_state: AppState) -> str:
    """
    获取基准点对齐系统状态摘要

    Args:
        app_state: 应用状态对象

    Returns:
        状态摘要字符串
    """
    if not app_state.alignment_enabled:
        return "基准点对齐: 禁用"

    is_healthy, issues = validate_alignment_system(app_state)

    if is_healthy:
        if app_state.last_alignment_success:
            return "基准点对齐: 正常运行"
        else:
            return f"基准点对齐: 搜索中({app_state.alignment_fail_count}次失败)"
    else:
        return f"基准点对齐: 异常({len(issues)}个问题)"
