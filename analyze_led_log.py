import re
import os
from datetime import datetime

# 定义日志文件路径
# 获取脚本所在目录的绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'led_digit_detection.log')  # 使用绝对路径
# log_file = 'led_digit_detection.log'
# 如果要指定绝对路径，可以取消下面这行的注释
# log_file = r'C:\Users\<USER>\Desktop\日志分析\led_detection.log'

# ===== 35点位扩展配置 =====
# 配对关系映射 (ROI 1-16 ↔ ROI 17-32)
PAIR_MAPPING = {
    1: 17, 2: 18, 3: 19, 4: 20, 5: 21, 6: 22, 7: 23, 8: 24,
    9: 25, 10: 26, 11: 27, 12: 28, 13: 29, 14: 30, 15: 31, 16: 32,
    17: 1, 18: 2, 19: 3, 20: 4, 21: 5, 22: 6, 23: 7, 24: 8,
    25: 9, 26: 10, 27: 11, 28: 12, 29: 13, 30: 14, 31: 15, 32: 16
}

# 独立监控LED映射 (LED名称 -> ROI编号)
SPECIAL_LEDS = {
    'G33': 33,
    'R1': 34,   # 对应日志中的R1
    'R2': 35    # 对应日志中的R2
}

def map_roi_to_sequence(roi_num):
    """
    将ROI编号映射到序列编号 (用于配对模式分析)
    G1-G16 -> 1-16 (直接映射)
    G17-G32 -> 1-16 (减16映射)
    其他 -> None (不参与序列分析)
    """
    if 1 <= roi_num <= 16:
        return roi_num
    elif 17 <= roi_num <= 32:
        return roi_num - 16
    else:
        return None

def get_paired_roi(roi_num):
    """获取配对的ROI编号"""
    return PAIR_MAPPING.get(roi_num, None)

def parse_timestamp(timestamp_str):
    """解析时间戳字符串为datetime对象"""
    try:
        return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
    except ValueError:
        return None

def calculate_time_diff_seconds(start_time_str, end_time_str):
    """计算两个时间戳之间的秒数差"""
    start_dt = parse_timestamp(start_time_str)
    end_dt = parse_timestamp(end_time_str)
    if start_dt and end_dt:
        return (end_dt - start_dt).total_seconds()
    return 0.0

def analyze_led_cycles(log_file_path=None):
    """
    分析LED日志文件，计算完美配对点亮周期的数量和独立LED监控

    Args:
        log_file_path: 日志文件路径，如果为None则使用默认路径

    35点位模式定义:
    1. G1-G32配对点亮模式：16步完美周期，每步一对LED同时点亮
       - 配对关系：ROI 1↔17, ROI 2↔18, ..., ROI 16↔32
       - 序列顺序：从ROI 1开始，按1→2→3→...→16的顺序
       - 独占性：每个时刻只有一对对应的ROI为ON，其他30个ROI为OFF

    2. G33、R1、R2独立监控：分析ON状态持续时间
       - 判断标准：累计ON时间超过1秒为"好"
       - 结果：都好发送1到M13，有问题发送3到M13
    """
    # 使用传入的路径，如果没有传入则使用默认逻辑
    if log_file_path:
        log_file = log_file_path
    else:
        # 获取脚本所在目录的绝对路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(script_dir, 'led_digit_detection.log')
    
    # 检查文件是否存在
    if not os.path.exists(log_file):
        print(f"文件 {log_file} 不存在!")
        return None
    
    start_time = datetime.now()
    print(f"开始时间: {start_time.strftime('%H:%M:%S')}")
    
    try:
        # 尝试使用UTF-8编码读取
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            # 如果UTF-8失败，尝试使用latin-1编码（可以读取任何字节）
            print("UTF-8编码读取失败，尝试使用latin-1编码...")
            with open(log_file, 'r', encoding='latin-1') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return
    
    print(f"文件大小: {len(content)/1024:.2f} KB")
    
    # 正则表达式模式: 匹配ROI状态变化为ON事件 (带有brightest LED标记)
    # 支持G1-G35的brightest LED检测，用于配对模式分析
    roi_on_pattern = re.compile(r'INFO - LED G(\d+) state OFF->ON.*?\(brightest LED\)')

    # 匹配所有LED的状态信息 (G1-G35, R1-R2)
    # G1-G35: LED G(\d+): Status=(\w+)
    # R1-R2: LED R(\d+): Status=(\w+)
    status_pattern_g = re.compile(r'INFO - LED G(\d+): Status=(\w+)')
    status_pattern_r = re.compile(r'INFO - LED R(\d+): Status=(\w+)')

    # 匹配状态变化事件 (用于时间分析)
    state_change_pattern_g = re.compile(r'INFO - LED G(\d+) state (\w+)->(\w+)')
    state_change_pattern_r = re.compile(r'INFO - LED R(\d+) state (\w+)->(\w+)')

    # 提取时间戳的模式
    timestamp_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})')
    
    # 查找所有ROI状态变化为ON的事件
    roi_on_events = []
    for match in roi_on_pattern.finditer(content):
        # 获取包含这个匹配的行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]
        
        # 从行中提取时间戳
        timestamp_match = timestamp_pattern.search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else "unknown"
        
        roi_num = int(match.group(1))
        roi_on_events.append((timestamp, roi_num))
    
    print(f"找到 {len(roi_on_events)} 个ROI点亮事件")
    
    # 查找所有LED状态信息 (G1-G35, R1-R2)
    status_events = []

    # 处理G系列LED状态
    for match in status_pattern_g.finditer(content):
        # 获取包含这个匹配的行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]

        # 从行中提取时间戳
        timestamp_match = timestamp_pattern.search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else "unknown"

        roi_num = int(match.group(1))  # G1-G35对应ROI 1-35
        status = match.group(2)
        status_events.append((timestamp, roi_num, status))

    # 处理R系列LED状态 (R1->ROI 34, R2->ROI 35)
    for match in status_pattern_r.finditer(content):
        # 获取包含这个匹配的行
        line_start = content.rfind('\n', 0, match.start()) + 1
        line_end = content.find('\n', match.start())
        if line_end == -1:
            line_end = len(content)
        line = content[line_start:line_end]

        # 从行中提取时间戳
        timestamp_match = timestamp_pattern.search(line)
        timestamp = timestamp_match.group(1) if timestamp_match else "unknown"

        r_num = int(match.group(1))  # R1, R2
        roi_num = 33 + r_num  # R1->ROI 34, R2->ROI 35
        status = match.group(2)
        status_events.append((timestamp, roi_num, status))

    print(f"找到 {len(status_events)} 个LED状态记录 (G1-G35, R1-R2)")
    
    # 如果没有找到事件，可能是正则表达式匹配有问题
    if len(roi_on_events) == 0:
        print("警告：未找到任何ROI点亮事件，请检查日志格式或正则表达式")
        # 输出文件前200个字符供调试
        print(f"文件开始内容: {content[:200]}...")
        return
    
    # 按时间戳排序
    roi_on_events.sort(key=lambda x: x[0])
    status_events.sort(key=lambda x: x[0])

    # ===== 配对模式完美周期分析 =====
    perfect_cycles = 0
    current_sequence = []
    perfect_cycle_details = []

    def get_led_status_snapshot(target_time, status_events, time_window_ms=100):
        """
        获取指定时间点附近的LED状态快照

        Args:
            target_time: 目标时间戳
            status_events: 所有状态事件
            time_window_ms: 时间窗口（毫秒）

        Returns:
            dict: {roi_num: status} 的状态快照
        """
        target_dt = parse_timestamp(target_time)
        if not target_dt:
            return {}

        # 计算时间窗口
        window_seconds = time_window_ms / 1000.0

        # 获取时间窗口内的状态记录
        snapshot = {}
        for timestamp, roi_num, status in status_events:
            if 1 <= roi_num <= 32:  # 只关注ROI 1-32
                event_dt = parse_timestamp(timestamp)
                if event_dt:
                    time_diff = abs((event_dt - target_dt).total_seconds())
                    if time_diff <= window_seconds:
                        # 如果同一个ROI有多条记录，使用时间最接近的
                        if roi_num not in snapshot or time_diff < snapshot[roi_num][1]:
                            snapshot[roi_num] = (status, time_diff)

        # 只返回状态，不返回时间差
        return {roi_num: status_info[0] for roi_num, status_info in snapshot.items()}

    def find_paired_roi_activation_time(paired_roi, start_time, status_events, max_delay_ms=2000):
        """
        查找配对ROI在指定时间窗口内的激活时间

        Args:
            paired_roi: 配对ROI编号
            start_time: 开始时间戳
            status_events: 所有状态事件
            max_delay_ms: 最大延迟时间（毫秒）

        Returns:
            tuple: (found, activation_time, delay_ms)
        """
        start_dt = parse_timestamp(start_time)
        if not start_dt:
            return False, None, None

        max_delay_seconds = max_delay_ms / 1000.0

        for timestamp, roi_num, status in status_events:
            if roi_num == paired_roi and status == "ON":
                event_dt = parse_timestamp(timestamp)
                if event_dt:
                    delay_seconds = (event_dt - start_dt).total_seconds()
                    if 0 <= delay_seconds <= max_delay_seconds:
                        return True, timestamp, delay_seconds * 1000

        return False, None, None

    def validate_paired_step(brightest_roi, curr_time, next_time, status_events):
        """
        验证配对点亮步骤是否完美 - 2000ms时间窗口 + 严格独占性验证

        Args:
            brightest_roi: brightest LED的ROI编号
            curr_time: 当前时间戳
            next_time: 下一个时间戳
            status_events: 所有状态事件

        Returns:
            tuple: (is_valid, sequence_roi, error_msg)
        """
        # 1. 基本验证
        sequence_roi = map_roi_to_sequence(brightest_roi)
        if sequence_roi is None:
            return False, None, f"ROI {brightest_roi} 不参与配对序列分析"

        paired_roi = get_paired_roi(brightest_roi)
        if paired_roi is None:
            return False, sequence_roi, f"ROI {brightest_roi} 没有配对ROI"

        # 2. 配对时间窗口验证（2000ms）
        found_pair, pair_time, delay_ms = find_paired_roi_activation_time(paired_roi, curr_time, status_events)
        if not found_pair:
            return False, sequence_roi, f"配对ROI {paired_roi} 未在2000ms内点亮"

        # 使用配对ROI实际点亮的时间进行独占性验证
        validation_time = pair_time if pair_time else curr_time

        # 3. 严格独占性验证
        status_snapshot = get_led_status_snapshot(validation_time, status_events)
        if not status_snapshot:
            return False, sequence_roi, f"无法获取时间 {validation_time} 的状态快照"

        # 4. 检查独占性：只允许brightest_roi和paired_roi为ON
        expected_on_rois = {brightest_roi, paired_roi}
        actual_on_rois = {roi for roi, status in status_snapshot.items()
                         if status == "ON" and 1 <= roi <= 32}

        # 检查违反独占性的ROI
        unexpected_on = actual_on_rois - expected_on_rois
        if unexpected_on:
            return False, sequence_roi, f"违反独占性：非配对ROI {list(unexpected_on)} 异常点亮"

        # 检查配对是否完整
        missing_on = expected_on_rois - actual_on_rois
        if missing_on:
            return False, sequence_roi, f"配对不完整：ROI {list(missing_on)} 未点亮"

        return True, sequence_roi, f"配对验证通过：ROI {brightest_roi}+{paired_roi} (延迟{delay_ms:.1f}ms)，独占性OK"
    
    # 分析配对点亮事件序列
    for i in range(len(roi_on_events) - 1):
        curr_time, curr_roi = roi_on_events[i]
        next_time, next_roi = roi_on_events[i + 1]

        # 使用新的配对验证逻辑
        is_valid, sequence_roi, error_msg = validate_paired_step(curr_roi, curr_time, next_time, status_events)

        if not is_valid:
            print(f"配对验证失败: {error_msg}")
            current_sequence = []
            # 如果是序列开始的ROI 1，尝试重新开始
            if sequence_roi == 1:
                current_sequence = [1]
            continue

        # 检查序列顺序
        if not current_sequence:  # 新序列开始
            if sequence_roi == 1:  # 必须从序列1开始
                current_sequence = [sequence_roi]
                print(f"开始新的配对序列: ROI {curr_roi} (序列{sequence_roi}) + 配对ROI {get_paired_roi(curr_roi)}")
            else:
                print(f"序列必须从序列1开始，但从序列{sequence_roi}开始")
                continue
        else:
            # 检查是否是正确的下一个序列号
            expected_next = current_sequence[-1] + 1
            if expected_next > 16:
                expected_next = 1

            if sequence_roi == expected_next:
                current_sequence.append(sequence_roi)
                print(f"配对序列继续: ROI {curr_roi} (序列{sequence_roi}) + 配对ROI {get_paired_roi(curr_roi)}")
            else:
                print(f"序列中断: 期望序列{expected_next}，但得到序列{sequence_roi}")
                current_sequence = []
                if sequence_roi == 1:  # 如果是序列1，开始新序列
                    current_sequence = [1]
                    print(f"重新开始配对序列: ROI {curr_roi} (序列{sequence_roi})")
                continue

        # 检查是否完成了一个完整的配对周期 (序列1-16)
        if len(current_sequence) == 16:
            # 验证序列是否为1-16的顺序
            if current_sequence == list(range(1, 17)):
                perfect_cycles += 1
                cycle_start = roi_on_events[i-15][0]  # 第一个ROI的时间戳
                cycle_end = next_time  # 当前周期结束时间
                perfect_cycle_details.append((perfect_cycles, cycle_start, cycle_end))
                print(f"发现第 {perfect_cycles} 个完美配对周期: {current_sequence}")
                print(f"  开始时间: {cycle_start}")
                print(f"  结束时间: {cycle_end}")
            else:
                print(f"发现周期但序列顺序不正确: {current_sequence}")

            # 重置序列开始新的周期检测
            current_sequence = []
    
    # ===== G33、R1、R2独立监控分析 =====
    print("\n" + "="*50)
    print("开始G33、R1、R2独立监控分析...")
    print("="*50)

    def analyze_special_led_duration(led_name, roi_num, state_change_events):
        """分析特殊LED的ON状态持续时间"""
        on_periods = []
        current_on_start = None

        # 提取该LED的状态变化事件
        led_events = []
        for match in state_change_events:
            line_start = content.rfind('\n', 0, match.start()) + 1
            line_end = content.find('\n', match.start())
            if line_end == -1:
                line_end = len(content)
            line = content[line_start:line_end]

            # 提取时间戳
            timestamp_match = timestamp_pattern.search(line)
            timestamp = timestamp_match.group(1) if timestamp_match else "unknown"

            # 根据LED类型提取信息
            if led_name.startswith('G'):
                led_num = int(match.group(1))
                from_state = match.group(2)
                to_state = match.group(3)
                if led_num == roi_num:
                    led_events.append((timestamp, from_state, to_state))
            else:  # R系列
                led_num = int(match.group(1))
                from_state = match.group(2)
                to_state = match.group(3)
                if led_num == (roi_num - 33):  # R1->1, R2->2
                    led_events.append((timestamp, from_state, to_state))

        # 如果没有状态变化事件，检查LED是否持续ON状态
        if not led_events:
            # 查找该LED的所有状态记录，检查是否一直是ON
            if led_name.startswith('G'):
                led_pattern = re.compile(rf'LED G{roi_num}: Status=(\w+)')
            else:  # R系列
                led_pattern = re.compile(rf'LED {led_name}: Status=(\w+)')

            led_status_records = []
            for match in led_pattern.finditer(content):
                line_start = content.rfind('\n', 0, match.start()) + 1
                line_end = content.find('\n', match.start())
                if line_end == -1:
                    line_end = len(content)
                line = content[line_start:line_end]

                # 提取时间戳
                timestamp_match = timestamp_pattern.search(line)
                if timestamp_match:
                    timestamp = timestamp_match.group(1)
                    status = match.group(1)
                    led_status_records.append((timestamp, status))

            # 检查是否所有记录都是ON状态
            if led_status_records:
                all_on = all(status == 'ON' for _, status in led_status_records)
                if all_on and len(led_status_records) >= 2:
                    # 计算从第一条记录到最后一条记录的时间
                    first_timestamp = led_status_records[0][0]
                    last_timestamp = led_status_records[-1][0]
                    total_duration = calculate_time_diff_seconds(first_timestamp, last_timestamp)
                    if total_duration > 0:
                        on_periods.append(total_duration)
                        print(f"  {led_name}持续ON状态: {first_timestamp} 到 {last_timestamp} ({total_duration:.3f}秒)")
        else:
            # 有状态变化事件，需要检查初始状态
            # 首先检查LED在日志开始时的状态
            if led_name.startswith('G'):
                led_pattern = re.compile(rf'LED G{roi_num}: Status=(\w+)')
            else:  # R系列
                led_pattern = re.compile(rf'LED {led_name}: Status=(\w+)')

            # 找到第一条状态记录
            first_status_match = led_pattern.search(content)
            if first_status_match:
                first_status = first_status_match.group(1)
                # 提取第一条记录的时间戳
                line_start = content.rfind('\n', 0, first_status_match.start()) + 1
                line_end = content.find('\n', first_status_match.start())
                if line_end == -1:
                    line_end = len(content)
                first_line = content[line_start:line_end]
                first_timestamp_match = timestamp_pattern.search(first_line)

                if first_timestamp_match and first_status == "ON":
                    # 如果开始时就是ON状态，从第一条记录开始计时
                    current_on_start = first_timestamp_match.group(1)
                    print(f"  {led_name}开始时即为ON状态: {current_on_start}")

            # 处理状态变化事件
            for timestamp, from_state, to_state in led_events:
                if to_state == "ON" and current_on_start is None:
                    current_on_start = timestamp
                    print(f"  {led_name}开始ON: {timestamp}")
                elif from_state == "ON" and to_state == "OFF" and current_on_start is not None:
                    duration = calculate_time_diff_seconds(current_on_start, timestamp)
                    if duration > 0:
                        on_periods.append(duration)
                        print(f"  {led_name}ON时间段: {current_on_start} 到 {timestamp} ({duration:.3f}秒)")
                    current_on_start = None

            # 如果最后还在ON状态，计算到日志结束的时间
            if current_on_start is not None:
                # 找到日志中的最后一个时间戳
                last_timestamp = None
                for match in timestamp_pattern.finditer(content):
                    last_timestamp = match.group(1)

                if last_timestamp:
                    duration = calculate_time_diff_seconds(current_on_start, last_timestamp)
                    if duration > 0:
                        on_periods.append(duration)
                        print(f"  {led_name}最后ON状态: {current_on_start} 到 {last_timestamp} ({duration:.3f}秒)")

        total_duration = sum(on_periods)
        is_good = total_duration > 1.0  # 超过1秒为好

        return {
            'total_duration': total_duration,
            'is_good': is_good,
            'on_periods': on_periods,
            'period_count': len(on_periods)
        }

    # 分析G33
    g33_result = analyze_special_led_duration('G33', 33, state_change_pattern_g.finditer(content))
    print(f"G33分析结果:")
    print(f"  总ON时间: {g33_result['total_duration']:.3f}秒")
    print(f"  ON次数: {g33_result['period_count']}")
    print(f"  状态: {'好' if g33_result['is_good'] else '不好'} (需要>1秒)")

    # 分析R1
    r1_result = analyze_special_led_duration('R1', 34, state_change_pattern_r.finditer(content))
    print(f"R1分析结果:")
    print(f"  总ON时间: {r1_result['total_duration']:.3f}秒")
    print(f"  ON次数: {r1_result['period_count']}")
    print(f"  状态: {'好' if r1_result['is_good'] else '不好'} (需要>1秒)")

    # 分析R2
    r2_result = analyze_special_led_duration('R2', 35, state_change_pattern_r.finditer(content))
    print(f"R2分析结果:")
    print(f"  总ON时间: {r2_result['total_duration']:.3f}秒")
    print(f"  ON次数: {r2_result['period_count']}")
    print(f"  状态: {'好' if r2_result['is_good'] else '不好'} (需要>1秒)")

    # 综合判断
    all_special_leds_good = g33_result['is_good'] and r1_result['is_good'] and r2_result['is_good']
    special_leds_status = "GOOD" if all_special_leds_good else "BAD"

    print(f"\n特殊LED综合状态: {special_leds_status}")
    print(f"G33: {'OK' if g33_result['is_good'] else 'FAIL'}, R1: {'OK' if r1_result['is_good'] else 'FAIL'}, R2: {'OK' if r2_result['is_good'] else 'FAIL'}")

    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()

    print("\n" + "="*50)
    print(f"分析耗时: {duration:.2f} 秒")
    print("="*50)
    
    # 输出完美周期详情
    if perfect_cycles > 0:
        print("\n完美配对周期详情:")
        for idx, start, end in perfect_cycle_details:
            print(f"周期 {idx}: {start} 到 {end}")

    # --- 在所有输出结束后，打印固定格式的结果 --- #
    print(f"Perfect Cycles Found: {perfect_cycles}")
    print(f"G33 Duration Analysis: {g33_result['total_duration']:.3f}s ({'GOOD' if g33_result['is_good'] else 'BAD'})")
    print(f"R1 Duration Analysis: {r1_result['total_duration']:.3f}s ({'GOOD' if r1_result['is_good'] else 'BAD'})")
    print(f"R2 Duration Analysis: {r2_result['total_duration']:.3f}s ({'GOOD' if r2_result['is_good'] else 'BAD'})")
    print(f"Special LEDs Status: {special_leds_status}")

    # --- 返回分析结果供程序调用 --- #
    return {
        'perfect_cycles': perfect_cycles,
        'g33_result': g33_result,
        'r1_result': r1_result,
        'r2_result': r2_result,
        'special_leds_status': special_leds_status
    }

if __name__ == "__main__":
    print("="*50)
    print("LED顺序点亮周期分析程序")
    print("="*50)
    analyze_led_cycles() 