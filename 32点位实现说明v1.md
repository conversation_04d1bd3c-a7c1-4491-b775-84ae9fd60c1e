# LED 32点位配对点亮分析逻辑文档

## 概述

本文档描述了LED数字检测系统从16点位扩展到32点位配对点亮模式的逻辑需求和实现方法。系统现在支持32个LED点位（ROI 1-32），采用配对同时点亮的工作模式。

## 业务需求

### 原始需求（16点位）
- **点位范围**：ROI 1 到 ROI 16
- **点亮模式**：单点顺序点亮
- **完美周期**：按顺序 ROI 1→2→3→...→16 点亮
- **独占性**：每个时刻只有一个ROI为ON状态

### 新需求（32点位配对模式）
- **点位范围**：ROI 1 到 ROI 32（总共32个LED点位）
- **配对关系**：ROI 1↔ROI 17, ROI 2↔ROI 18, ..., ROI 16↔ROI 32
- **点亮模式**：配对同时点亮
- **完美周期**：仍然是16步，但每步都是一对LED同时点亮
- **独占性**：每个时刻只有一对对应的ROI为ON状态

## 核心逻辑规则

### 1. 配对关系定义
```
ROI 1  ↔ ROI 17
ROI 2  ↔ ROI 18
ROI 3  ↔ ROI 19
...
ROI 16 ↔ ROI 32
```

**映射公式**：
- 对于ROI N (1≤N≤16)：配对ROI = N + 16
- 对于ROI M (17≤M≤32)：配对ROI = M - 16

### 2. 完美周期定义

一个完美周期包含16个步骤：

| 步骤 | 期望点亮的ROI对 | 其他ROI状态 |
|------|----------------|-------------|
| 1    | ROI 1 + ROI 17 | 全部OFF     |
| 2    | ROI 2 + ROI 18 | 全部OFF     |
| 3    | ROI 3 + ROI 19 | 全部OFF     |
| ...  | ...            | ...         |
| 16   | ROI 16 + ROI 32| 全部OFF     |

### 3. 验证规则

#### 顺序性验证
- 必须从ROI 1开始
- 按照1→2→3→...→16的严格顺序
- 任何顺序中断都会重置周期检测

#### 配对性验证
- 每个时间区间内，必须有且仅有一对对应的ROI为ON状态
- 验证配对ROI确实同时点亮
- 检测并报告任何非配对ROI的异常点亮

#### 独占性验证
- 在ROI N的点亮期间，只允许ROI N和ROI (N+16)为ON状态
- 其他30个ROI必须全部为OFF状态
- 任何违反独占性的情况都会导致周期无效

## 实现方法

### 1. 日志解析策略

#### Brightest LED识别
- 系统中每对LED同时点亮时，其中一个会被标记为"brightest LED"
- 程序只跟踪"brightest LED"事件来确定点亮序列
- 通过配对关系验证另一个LED是否同时点亮

#### 序列映射
- 如果检测到的brightest LED是ROI 17-32，将其映射回ROI 1-16进行序列分析
- 例如：检测到G17 brightest LED → 映射为序列中的ROI 1
- 例如：检测到G18 brightest LED → 映射为序列中的ROI 2

### 2. 状态验证流程

#### 时间区间分析
1. 提取每个brightest LED事件的时间戳
2. 定义时间区间：从当前事件到下一个事件
3. 分析该区间内所有ROI的状态变化

#### 配对验证算法
1. 根据brightest LED确定期望的配对ROI
2. 检查时间区间内是否只有这一对ROI为ON状态
3. 验证配对ROI确实存在ON状态记录
4. 报告任何异常的ROI状态

### 3. 周期检测逻辑

#### 序列构建
- 维护当前检测序列：`current_sequence = []`
- 每个有效的ROI事件添加到序列中
- 序列必须从1开始，按顺序增长

#### 完美周期判定
- 当序列长度达到16时进行验证
- 验证序列是否为`[1, 2, 3, ..., 16]`
- 记录周期的开始和结束时间
- 重置序列开始新的周期检测

#### 异常处理
- 顺序中断：重置序列，从下一个ROI 1开始
- 配对异常：重置序列，报告具体异常信息
- 状态异常：重置序列，记录异常ROI

## 输出结果

### 统计信息
- 完美周期总数
- 每个周期的详细时间信息
- 分析耗时和性能数据

### 异常报告
- 顺序中断事件及原因
- 配对异常详情
- 非预期的ROI状态变化

### 验证确认
- 每个完美周期的序列验证
- 配对关系确认信息
- 系统工作状态评估

## 应用场景

### 质量控制
- 验证LED显示设备按预期顺序工作
- 确保配对LED同步点亮
- 检测硬件故障或控制异常

### 性能监控
- 统计完美周期的频率和稳定性
- 分析点亮时序的准确性
- 监控系统长期运行状态

### 故障诊断
- 快速定位异常的ROI点位
- 分析配对关系是否正确
- 提供详细的错误信息用于维修

## 技术优势

1. **向后兼容**：保持原有16点位的分析逻辑
2. **智能识别**：自动处理brightest LED标记的变化
3. **精确验证**：严格的配对和独占性检查
4. **性能优化**：高效的时间区间分析算法
5. **详细诊断**：全面的异常检测和报告机制
