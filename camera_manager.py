import cv2
import time
from app_state import AppState
from constants import *

def apply_camera_settings(cap: cv2.VideoCapture, app_state: AppState):
    """将 AppState 中的设置应用到给定的摄像头对象"""
    if not cap or not cap.isOpened():
        print("错误：无法应用设置，摄像头未打开。")
        return False

    # 检查分辨率索引有效性
    if not (0 <= app_state.current_resolution_index < len(RESOLUTION_PRESETS)):
         print(f"错误：无效的分辨率索引 {app_state.current_resolution_index}。将尝试使用默认索引 {DEFAULT_RESOLUTION_INDEX}。")
         app_state.current_resolution_index = DEFAULT_RESOLUTION_INDEX
         if not (0 <= app_state.current_resolution_index < len(RESOLUTION_PRESETS)): # 再次检查默认值是否有效
              print("错误：默认分辨率索引也无效。无法设置分辨率。")
              return False # 严重错误

    width, height = RESOLUTION_PRESETS[app_state.current_resolution_index]
    exposure = app_state.exposure_value
    brightness = app_state.brightness_value

    print(f"应用摄像头设置: 分辨率 {width}x{height}, 曝光 {exposure}, 亮度 {brightness}")

    settings_ok = True

    # 设置分辨率
    print(f"尝试设置分辨率: {width}x{height}")
    res_set_w = cap.set(cv2.CAP_PROP_FRAME_WIDTH, float(width))
    res_set_h = cap.set(cv2.CAP_PROP_FRAME_HEIGHT, float(height))
    if not (res_set_w and res_set_h):
        print("警告：设置分辨率可能失败。")
        settings_ok = False

    # 设置曝光 (尝试关闭自动曝光)
    print(f"尝试设置曝光: {exposure}")
    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 1) # 尝试禁用 (旧方法)
    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25) # 尝试禁用 (UVC)
    cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0) # 尝试禁用
    exposure_set = cap.set(cv2.CAP_PROP_EXPOSURE, float(exposure))
    if not exposure_set:
        print("警告：设置曝光失败。")
        settings_ok = False

    # 设置亮度
    print(f"尝试设置亮度: {brightness}")
    brightness_set = cap.set(cv2.CAP_PROP_BRIGHTNESS, float(brightness))
    if not brightness_set:
        print("警告：设置亮度失败。")
        settings_ok = False

    # 等待设置生效
    time.sleep(0.5)

    # 读取实际值进行验证
    actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
    actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
    actual_exposure = cap.get(cv2.CAP_PROP_EXPOSURE)
    actual_brightness = cap.get(cv2.CAP_PROP_BRIGHTNESS)
    print(f"实际应用值: 分辨率 {int(actual_width)}x{int(actual_height)}, 曝光 {actual_exposure}, 亮度 {actual_brightness}")

    # 检查分辨率是否接近期望值
    if abs(actual_width - width) > 10 or abs(actual_height - height) > 10:
         print(f"警告：实际分辨率 ({int(actual_width)}x{int(actual_height)}) 与目标 ({width}x{height}) 不符。")
         # settings_ok = False # 不一定算失败，只是警告

    # 检查曝光和亮度是否成功设置
    if abs(actual_exposure - exposure) > 1.0 and exposure_set:
        print(f"警告：实际曝光值 ({actual_exposure}) 与设置值 ({exposure}) 不符。")
    if abs(actual_brightness - brightness) > 5.0 and brightness_set:
        print(f"警告：实际亮度值 ({actual_brightness}) 与设置值 ({brightness}) 不符。")

    if not settings_ok:
         print("完成摄像头设置，但部分参数可能未成功应用。")
    else:
         print("摄像头参数设置完成。")

    return settings_ok # 返回是否所有设置都报告成功

def initialize_camera(app_state: AppState):
    """尝试初始化摄像头，应用设置，并更新 AppState"""
    print(f"尝试打开主摄像头索引 {DEFAULT_CAMERA_INDEX}...")
    app_state.active_cam_idx = DEFAULT_CAMERA_INDEX
    cap = cv2.VideoCapture(app_state.active_cam_idx, cv2.CAP_DSHOW) # 尝试 DSHOW 后端

    if not cap.isOpened():
        print(f"使用 DSHOW 后端打开主摄像头 {app_state.active_cam_idx} 失败。尝试默认后端...")
        if cap: cap.release()
        cap = cv2.VideoCapture(app_state.active_cam_idx)

        if not cap.isOpened():
             print(f"默认后端也失败。尝试备用摄像头索引 {FALLBACK_CAMERA_INDEX}...")
             if cap: cap.release()
             app_state.active_cam_idx = FALLBACK_CAMERA_INDEX
             cap = cv2.VideoCapture(app_state.active_cam_idx, cv2.CAP_DSHOW) # 备用也尝试 DSHOW

             if not cap.isOpened():
                  print(f"使用 DSHOW 后端打开备用摄像头 {app_state.active_cam_idx} 失败。尝试默认后端...")
                  if cap: cap.release()
                  cap = cv2.VideoCapture(app_state.active_cam_idx)

                  if not cap.isOpened():
                       print("错误：无法打开任何摄像头。请检查连接和驱动。")
                       if cap: cap.release()
                       app_state.cap = None
                       return False # 初始化失败
                  else:
                       print(f"成功使用默认后端打开备用摄像头 {app_state.active_cam_idx}")
             else:
                  print(f"成功使用 DSHOW 后端打开备用摄像头 {app_state.active_cam_idx}")
        else:
             print(f"成功使用默认后端打开主摄像头 {app_state.active_cam_idx}")
    else:
        print(f"成功使用 DSHOW 后端打开主摄像头 {app_state.active_cam_idx}")

    # 摄像头已打开，应用设置
    if apply_camera_settings(cap, app_state):
        app_state.cap = cap
        return True
    else:
        print("警告：摄像头已打开，但应用设置时遇到问题。")
        app_state.cap = cap # 仍然设置 cap 对象，但可能功能不正常
        return True # 报告成功打开，但设置可能有问题

def release_camera(app_state: AppState):
    """释放摄像头资源"""
    if app_state.cap and app_state.cap.isOpened():
        print("释放摄像头资源...")
        app_state.cap.release()
        app_state.cap = None
