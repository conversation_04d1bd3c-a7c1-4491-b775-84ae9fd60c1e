"""
ROI微调功能模块
提供统一的ROI位置和尺寸微调功能，适用于所有ROI选择场景
"""

import cv2
from constants import *

# 微调相关常量
FINE_TUNE_STEP_DEFAULT = 2   # 默认微调步长(像素)
FINE_TUNE_STEP_MIN = 1       # 最小微调步长
FINE_TUNE_STEP_MAX = 10      # 最大微调步长


def handle_roi_fine_tune(app_state, key):
    """
    处理ROI微调功能 - 统一接口，适用于所有ROI选择场景
    
    Args:
        app_state: 应用状态对象
        key: 按键码
        
    Returns:
        bool: 是否处理了按键事件
    """
    # 检查是否有当前ROI可以微调
    if not app_state.current_rect:
        return False
    
    # 检查是否在支持微调的状态下
    supported_states = [
        CALIB_STATE_BASE_POINTS_SELECT,
        CALIB_STATE_LED_ROI_SELECT,
        CALIB_STATE_LED_EDIT,
        CALIB_STATE_DIGIT_ROI_SELECT_1,
        CALIB_STATE_DIGIT_ROI_SELECT_2,
        CALIB_STATE_DIGIT_SEGMENT_SELECT
    ]
    
    if app_state.current_calib_state not in supported_states:
        return False
    
    x, y, w, h = app_state.current_rect
    step = app_state.fine_tune_step
    modified = False
    
    # WASD键移动ROI位置
    if key == ord('a'):  # A键 - 左移
        x = max(0, x - step)
        modified = True
        print(f"微调: 左移 {step}px")
    elif key == ord('d'):  # D键 - 右移
        x = x + step
        modified = True
        print(f"微调: 右移 {step}px")
    elif key == ord('w'):  # W键 - 上移
        y = max(0, y - step)
        modified = True
        print(f"微调: 上移 {step}px")
    elif key == ord('s'):  # S键 - 下移
        y = y + step
        modified = True
        print(f"微调: 下移 {step}px")
    
    # Shift + WASD 调整ROI大小
    elif key == ord('A'):  # Shift + A - 缩小宽度
        w = max(10, w - step)
        modified = True
        print(f"微调: 宽度减少 {step}px")
    elif key == ord('D'):  # Shift + D - 增加宽度
        w = w + step
        modified = True
        print(f"微调: 宽度增加 {step}px")
    elif key == ord('W'):  # Shift + W - 缩小高度
        h = max(10, h - step)
        modified = True
        print(f"微调: 高度减少 {step}px")
    elif key == ord('S'):  # Shift + S - 增加高度
        h = h + step
        modified = True
        print(f"微调: 高度增加 {step}px")
    
    # 数字键1-9设置步长
    elif 49 <= key <= 57:  # 数字键1-9
        new_step = key - 48  # 转换为数字1-9
        if FINE_TUNE_STEP_MIN <= new_step <= FINE_TUNE_STEP_MAX:
            app_state.fine_tune_step = new_step
            print(f"微调步长设置为: {new_step}px")
            return True
    
    # 应用修改
    if modified:
        app_state.current_rect = (x, y, w, h)
        return True
    
    return False


def draw_fine_tune_info(app_state, frame):
    """
    在界面上绘制微调信息
    
    Args:
        app_state: 应用状态对象
        frame: 要绘制的图像帧
    """
    # 只在有当前ROI时显示微调信息
    if not app_state.current_rect:
        return
    
    # 检查是否在支持微调的状态下
    supported_states = [
        CALIB_STATE_BASE_POINTS_SELECT,
        CALIB_STATE_LED_ROI_SELECT,
        CALIB_STATE_LED_EDIT,
        CALIB_STATE_DIGIT_ROI_SELECT_1,
        CALIB_STATE_DIGIT_ROI_SELECT_2,
        CALIB_STATE_DIGIT_SEGMENT_SELECT
    ]
    
    if app_state.current_calib_state not in supported_states:
        return
    
    # 获取图像尺寸
    _h, _w = frame.shape[:2]

    # 在右上角显示微调信息
    info_x = _w - 200
    info_y = 30
    
    # 背景框
    cv2.rectangle(frame, (info_x - 5, info_y - 20), (info_x + 190, info_y + 60), (0, 0, 0), -1)
    cv2.rectangle(frame, (info_x - 5, info_y - 20), (info_x + 190, info_y + 60), (255, 255, 255), 1)
    
    # 显示步长信息
    step_text = f"Fine-tune Step: {app_state.fine_tune_step}px"
    cv2.putText(frame, step_text, (info_x, info_y), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    
    # 显示控制说明
    control_text = "WASD:Move | Shift+WASD:Size"
    cv2.putText(frame, control_text, (info_x, info_y + 20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
    
    # 显示步长设置说明
    step_control_text = "1-9: Set step size"
    cv2.putText(frame, step_control_text, (info_x, info_y + 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)


def get_fine_tune_help():
    """
    获取微调功能的帮助信息
    
    Returns:
        str: 帮助信息
    """
    return """
ROI Fine-tune Controls (when ROI is selected):
Position adjustment:
- WASD: Move ROI position (←→↑↓)
- Number keys 1-9: Set adjustment step size (default: 2px)

Size adjustment:
- Shift+W: Decrease height
- Shift+S: Increase height  
- Shift+A: Decrease width
- Shift+D: Increase width

Status display:
- Top-right corner shows current step size
- Console outputs adjustment feedback
"""


def init_fine_tune_state(app_state):
    """
    初始化微调相关状态
    
    Args:
        app_state: 应用状态对象
    """
    if not hasattr(app_state, 'fine_tune_step'):
        app_state.fine_tune_step = FINE_TUNE_STEP_DEFAULT
