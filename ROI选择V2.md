# LED数码管检测系统 - 完整操作指南 v2.0

## 概述

本系统是一个专业的工业视觉检测系统，基于OpenCV开发，支持LED灯珠状态检测、数码管字符识别，以及基于"88"触发的自动化分析流程。系统具备基准点自动对齐、实时阈值调整、模板复制等高级功能。

### 系统特色
- 🎯 **三阶段工作流程**: 摄像头设置 → 校准配置 → 实时检测
- 🔧 **基准点自动对齐**: 自动补偿产品位置偏移
- 📊 **智能分析流程**: "88"触发的自动化日志记录和分析
- ⚡ **实时优化**: 支持检测过程中的参数调整
- 🎨 **直观界面**: 丰富的视觉反馈和操作提示

---

## 系统架构

### 工作模式
- **摄像头设置模式** (`MODE_CAMERA_SETTINGS`): 调整摄像头参数
- **校准模式** (`MODE_CALIBRATION`): 配置ROI区域和检测参数  
- **检测模式** (`MODE_DETECTION`): 实时检测和自动化分析

### 配置文件
- `combined_config.json`: 存储所有配置信息（摄像头参数、ROI坐标、阈值等）
- `led_detection.log`: 自动分析过程中的日志文件

---

## 第一阶段：摄像头设置

### 目标
调整摄像头参数以获得最佳图像质量，为后续校准和检测奠定基础。

### 操作步骤

#### 基本参数调整
| 按键 | 功能 | 调整幅度 | 说明 |
|------|------|----------|------|
| `T` / `t` | 分辨率切换 | 循环切换 | 640x480 → 1920x1080 等 |
| `E` / `e` | 曝光调整 | ±1.0 | 大写增加，小写减少 |
| `B` / `b` | 亮度调整 | ±10.0 | 大写增加，小写减少 |

#### 操作流程
1. **启动程序** - 自动进入摄像头设置模式
2. **调整参数** - 根据实际光照条件调整
3. **保存设置** - `S` 保存当前摄像头参数
4. **进入校准** - `Enter` 确认设置并进入校准模式

### 界面提示
```
Resolution: 1920x1080 ('T'/'t') | Exposure: -6.0 ('E'/'e') | Brightness: 0.0 ('B'/'b')
Press 'S' Save | 'Enter' Calibrate | 'Q' Quit
```

### 最佳实践
- **分辨率选择**: 根据检测精度需求选择，高分辨率提供更好精度但处理速度较慢
- **曝光调整**: 避免过曝或欠曝，确保LED和数码管清晰可见
- **亮度平衡**: 保证整体图像亮度适中，便于后续阈值设置

---

## 第二阶段：校准模式

### 校准主菜单

校准模式采用模块化设计，支持独立校准各个检测组件。

#### 主要选项
| 按键 | 功能 | 前置条件 | 说明 |
|------|------|----------|------|
| `B` | 基准点校准 | 无 | 🆕 设置自动对齐基准点 |
| `L` | LED校准 | 无 | 配置LED灯珠检测 |
| `D` | 数码管校准 | 无 | 配置数码管检测 |
| `E` | 编辑LEDs | 已完成LED校准 | 🆕 调整LED ROI位置 |
| `R` | 重新采样LEDs | 已完成LED校准 | 🆕 快速重新采样 |
| `S` | 保存并退出 | 无 | 保存配置并退出校准 |
| `Enter` | 进入检测模式 | 校准完整 | 开始实时检测 |

#### 界面提示
```
Calibration: 'B' Base Points | 'L' LED | 'D' Digit | 'E' Edit LEDs | 'R' Resample LEDs | 'S' Save&Exit Calib | 'Enter' Detect (if ready)
```

---

## 基准点校准系统 🆕

### 功能概述
基准点系统通过识别产品上的固定特征点，实现ROI坐标的自动对齐，补偿产品位置偏移。

### 操作流程

#### 1. 进入基准点选择
- 在校准主菜单按 `B`
- 系统提示选择第一个基准点

#### 2. 选择基准点
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标点击** | 选择基准点 | 点击产品上的明显特征点 |
| `R` | 重新选择 | 清空已选择的基准点 |
| `Esc` | 跳过基准点功能 | 使用固定坐标模式 |
| `Enter` | 完成选择 | 需要选择2个基准点 |

#### 3. 基准点选择要求
- **特征明显**: 选择螺丝孔、标记、清晰边角等
- **对比度高**: 避免平坦区域或光照不均匀位置
- **距离适当**: 两个基准点间应有一定距离
- **稳定可靠**: 选择不会变化的固定特征

#### 4. 完成校准
- 选择2个基准点后按 `Enter`
- 系统自动验证基准点配置
- 保存原始ROI坐标作为参考

### 界面提示
```
Base Point Calib: Select Feature Point 1/2
Click on clear feature points (screws/marks/corners) | 'R' Reset | 'Esc' Skip
```

### 视觉反馈
- **基准点标记**: 黄色圆圈 + 红色中心点
- **模板区域**: 青色边框显示模板提取区域
- **编号标识**: P1、P2 标识基准点序号

---

## LED校准流程

### 2.1 ROI区域选择

#### 基础操作
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标拖拽** | 选择ROI区域 | 左键拖拽创建矩形区域 |
| `Enter` | 确认当前ROI | 保存并进入下一个 |
| `N` | 跳过当前ROI | 该位置不设置LED检测 |
| `B` | 回退上一个ROI | 🆕 重新选择上一个ROI |
| `R` | 重置所有ROI | 清空所有已选择的ROI |
| `Esc` | 返回校准主菜单 | 退出LED校准 |

#### 高级功能 - 模板复制 🆕
1. **激活模板模式**: `T` - 使用前一个ROI作为模板
2. **模板操作**:
   - 鼠标移动显示模板预览（白色虚线框）
   - 点击位置放置相同大小的ROI
   - `Enter` 确认并继续复制下一个
3. **退出模板**: `Esc` - 退出模板模式

#### 高级功能 - WASD微调 🆕
选择ROI后可使用WASD进行精确调整：
| 按键 | 功能 | 默认步长 |
|------|------|----------|
| `W` / `S` | 上下移动 | 1像素 |
| `A` / `D` | 左右移动 | 1像素 |
| `Shift` + WASD | 大步长移动 | 10像素 |
| `Q` / `E` | 调整宽度 | ±1像素 |
| `Z` / `C` | 调整高度 | ±1像素 |

### 选择顺序
系统按照预定顺序进行LED ROI选择：
```
G1 → G2 → G3 → G4 → R1 → R2
(绿色LED优先，然后是红色LED)
```

### 界面提示
```
# 常规模式
LED Calib: Select ROI 1/6 (Green)
Drag mouse. 'Enter' Confirm | 'N' Next/Skip | 'B' Back | 'T' Template | 'R' Reset | Esc Back

# 模板模式  
Template Mode: Click to place | 'Enter' Confirm & Continue | 'Esc' Exit Template | 'R' Reset
```

### 视觉反馈
- **已确认ROI**: 绿色边框（绿色LED）/ 红色边框（红色LED）
- **正在选择**: 蓝色边框
- **模板预览**: 白色虚线边框
- **LED标签**: G1、G2、R1、R2 等标识

---

## LED编辑模式 🆕

### 功能概述
允许对已校准的LED ROI进行位置微调，无需重新完整校准。

### 进入条件
- 完成LED校准后，在校准主菜单按 `E`

### 操作方式

#### ROI选择
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标点击ROI** | 选择ROI | 直接点击要编辑的ROI |
| `1`-`6` | 数字键选择 | 快速选择对应编号的ROI |

#### ROI移动
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标拖拽** | 移动ROI | 拖拽选中的ROI到新位置 |
| **WASD微调** | 精确调整 | 像素级精确定位 |

#### 完成编辑
| 按键 | 功能 | 说明 |
|------|------|------|
| `Enter` | 保存所有更改 | 更新配置文件并退出编辑 |
| `Esc` | 退出编辑模式 | 不保存更改 |

### 视觉反馈
- **选中ROI**: 橙色边框 + 黄色角标
- **未选中ROI**: 正常绿色/红色边框
- **数字提示**: ROI外侧显示对应数字键

### 界面提示
```
# 未选中状态
Click ROI or press '1'-'6' to select | 'Enter' Confirm All | 'Esc' Exit Edit

# 选中状态  
Selected: G1 | Drag to move | '1'-'6' Select ROI | 'Enter' Confirm | 'Esc' Exit Edit
```

---

## LED快速重新采样 🆕

### 功能概述
保留现有ROI坐标，仅重新采集LED亮灭样本和重新计算阈值。适用于光照条件变化的情况。

### 使用场景
- 环境光照发生变化
- LED亮度特性改变
- 检测效果不佳需要重新优化阈值
- ROI位置正确但检测参数需要更新

### 操作流程
1. **进入快速重新采样**: 在校准主菜单按 `R`
2. **采集灭灯样本**: 确保所有LED熄灭，按 `C` 采集
3. **采集亮灯样本**: 确保所有LED点亮，按 `C` 采集  
4. **自动分析**: 系统自动计算新阈值并保存

### 优势
- ⚡ **快速**: 无需重新选择ROI坐标
- 🎯 **精确**: 针对当前环境优化检测参数
- 💾 **安全**: 保留原有ROI配置，只更新检测参数

---

## LED样本采集

### 2.2 灭灯样本采集

#### 操作要求
1. **确保所有定义的LED完全熄灭**
2. **保持稳定光照条件**
3. **避免手动遮挡或干扰**

#### 采集操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `C` | 采集样本 | 采集约10帧灭灯样本 |
| `Esc` | 返回ROI选择 | 重新调整ROI |

### 2.3 亮灯样本采集

#### 操作要求
1. **确保所有定义的LED完全点亮**
2. **保持与灭灯采集相同的光照条件**
3. **LED状态切换要完全**

#### 采集操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `C` | 采集样本 | 采集约10帧亮灯样本 |
| `Esc` | 返回灭灯采集 | 重新采集灭灯样本 |

### 2.4 阈值分析

#### 自动分析
系统基于采集的样本自动计算最优检测阈值：
- **灰度阈值**: 区分亮暗状态的灰度值
- **颜色阈值**: 区分绿色/红色LED的颜色通道值

#### 操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `A` | 分析并保存阈值 | 计算最优阈值并保存配置 |
| `Esc` | 返回亮灯采集 | 重新采集亮灯样本 |

#### 输出示例
```
LED 阈值计算完成: G(G=160.0, Gn=180.0), R(G=160.0, Rd=100.0)
LED 校准完成！可以按 'E' 进入编辑模式调整ROI位置。
```

---

## 数码管校准流程

### 3.1 捕捉"88"图像

#### 目标
捕捉数码管显示"88"的参考图像，用于后续ROI选择。

#### 操作步骤
1. **设置显示**: 确保数码管显示"88"
2. **捕捉图像**: `C` - 捕捉当前帧作为参考
3. **自动进入**: 成功捕捉后自动进入数码管ROI选择

### 3.2 选择数码管ROI

#### 选择顺序
1. **Digit 1 (左侧数字区域)**: 选择左侧数码管的整体区域
2. **Digit 2 (右侧数字区域)**: 选择右侧数码管的整体区域

#### 操作方式
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标拖拽** | 选择ROI | 框选数码管区域 |
| `Enter` | 确认ROI | 进入下一个数码管或段选择 |
| `Esc` | 返回上一步 | 重新捕捉"88"图像 |

### 3.3 选择段ROI

#### 7段显示器结构
每个数码管包含7个段（a-g）：
```
 aaa
f   b
 ggg  
e   c
 ddd
```

#### 选择流程
系统按照 a→b→c→d→e→f→g 的顺序为每个数码管选择段ROI。

#### 操作方式
| 操作 | 功能 | 说明 |
|------|------|------|
| **鼠标拖拽** | 选择段ROI | 精确框选每个段 |
| `Enter` | 确认段ROI | 进入下一个段 |
| `N` | 跳过当前段 | 该段不参与检测 |
| `P` | 返回上一段 | 重新选择上一个段 |
| `Esc` | 返回数码管ROI选择 | 重新选择数码管区域 |

#### 界面提示
```
Digit: Digit 1, Select Segment 'A' (1/7)
Drag mouse. 'Enter' Confirm, 'N' Skip, 'P' Prev, Esc Back to Digit ROI
```

### 3.4 捕捉背景图像

#### 目标
捕捉数码管完全关闭时的背景图像，用于亮度阈值计算。

#### 操作步骤
1. **关闭显示**: 确保数码管所有段都关闭
2. **捕捉背景**: `C` - 捕捉背景图像
3. **自动进入**: 成功捕捉后进入阈值调整

### 3.5 调整亮度阈值

#### 目标
设置区分数码管段亮灭状态的亮度阈值。

#### 操作方式
| 按键 | 功能 | 调整幅度 | 说明 |
|------|------|----------|------|
| `+` / `=` | 增加阈值 | +1.0 | 提高亮度判断标准 |
| `-` / `_` | 减少阈值 | -1.0 | 降低亮度判断标准 |
| `Enter` | 确认阈值 | - | 保存设置并完成校准 |
| `Esc` | 返回背景捕捉 | - | 重新捕捉背景图像 |

#### 视觉反馈
- **正确关闭的段**: 绿色边框
- **错误判断为亮的段**: 红色边框
- **实时阈值显示**: 当前阈值数值

#### 界面提示
```
Digit: Adjust Brightness Threshold (Current: 50.0)
Use '+' to increase, '-' to decrease. 'Enter' Confirm | Esc to go back
```

---

## 第三阶段：检测模式

### 功能概述
实时检测LED状态和数码管显示，支持基准点自动对齐和"88"触发的自动化分析流程。

### 3.1 基本操作

#### 模式控制
| 按键 | 功能 | 说明 |
|------|------|------|
| `C` | 返回校准模式 | 重新校准或调整参数 |
| `S` | 保存当前阈值 | 🆕 保存实时调整的阈值 |
| `L` | 快速保存LED参数 | 🆕 仅保存LED样本和阈值 |
| `B` | 切换基准点对齐 | 🆕 启用/禁用自动对齐功能 |
| `Q` | 退出程序 | 安全退出系统 |

### 3.2 实时阈值调整 🆕

#### LED阈值调整
| 按键 | 功能 | 调整幅度 | 说明 |
|------|------|----------|------|
| `g` / `G` | 绿色LED灰度阈值 | ±5.0 | 小写减少，大写增加 |
| `v` / `V` | 绿色LED绿色通道阈值 | ±5.0 | 小写减少，大写增加 |
| `y` / `Y` | 红色LED灰度阈值 | ±5.0 | 小写减少，大写增加 |
| `r` / `R` | 红色LED红色通道阈值 | ±5.0 | 小写减少，大写增加 |

#### 数码管阈值调整
| 按键 | 功能 | 调整幅度 | 说明 |
|------|------|----------|------|
| `+` / `=` | 增加数码管亮度阈值 | +1.0 | 提高亮度判断标准 |
| `-` / `_` | 减少数码管亮度阈值 | -1.0 | 降低亮度判断标准 |

#### 保存调整
- **完整保存**: `S` - 保存所有当前阈值到配置文件
- **快速保存**: `L` - 仅保存LED样本和阈值（不保存ROI坐标）

### 3.3 基准点自动对齐 🆕

#### 功能概述
系统实时检测基准点位置，自动调整所有ROI坐标以补偿产品位置偏移。

#### 对齐状态显示
- **Base Alignment: True** - 对齐成功，ROI坐标已自动调整
- **Base Alignment: Searching(N)** - 正在搜索基准点，N为失败次数
- **Base Alignment: Failed(N)** - 对齐失败，使用原始坐标
- **Base Alignment: Disabled** - 对齐功能已禁用

#### 控制操作
| 按键 | 功能 | 说明 |
|------|------|------|
| `B` | 切换对齐功能 | 启用/禁用基准点自动对齐 |

#### 最佳实践
- 确保基准点在摄像头视野内
- 基准点区域保持清洁，避免遮挡
- 产品位置变化不要过大（建议在±50像素范围内）

---

## "88"触发的自动化分析流程 🆕

### 功能概述
当数码管显示"88"时，系统自动启动LED状态日志记录和分析流程，实现完全自动化的检测分析。

### 状态机流程

#### 1. IDLE状态 - 等待触发
- **"88"触发**: 启动日志记录流程
- **"IP"/"1P"触发**: 发送信号到CPU地址11

#### 2. LOGGING状态 - 记录日志
- **持续时间**: 可配置（默认30秒）
- **记录内容**: 所有LED的实时状态和数值
- **状态显示**: 显示记录进度

#### 3. WAITING_TO_SIGNAL_12状态 - 发送完成信号
- **操作**: 发送信号1到CPU地址12
- **目的**: 通知外部系统日志记录完成

#### 4. ANALYZING状态 - 执行分析
- **分析内容**:
  - 完美周期数量统计
  - G33/R1/R2特殊LED监控
  - 持续时间分析
- **分析脚本**: 调用`analyze_led_cycles`函数

#### 5. SENDING_RESULT_10状态 - 发送分析结果
- **发送规则**:
  - 0个完美周期 → 发送3到地址10
  - 其他情况 → 发送1到地址10

#### 6. SENDING_RESULT_13状态 - 发送特殊LED结果
- **发送规则**:
  - G33/R1/R2状态GOOD → 发送1到地址13
  - G33/R1/R2状态BAD → 发送3到地址13

#### 7. CLEARING状态 - 清理日志
- **操作**: 清空日志文件
- **完成**: 返回IDLE状态等待下次触发

### 状态显示
```
# IDLE状态
MODE: Detection | 'C' Calibrate | 'B' Toggle Base Align | 'S' Save All | 'L' Save LED Only | 'Q' Quit

# LOGGING状态
Logging for '88' analysis... 15.3s / 30.0s
Logging active... Press 'Q' to Quit (will interrupt process)

# 其他处理状态
Processing '88' analysis (State: ANALYZING)... Please wait.
Press 'Q' to Quit (will interrupt process)
```

### 注意事项
- 分析过程中避免切换模式
- 确保日志文件路径可写
- 分析脚本需要正确导入

---

## 显示信息详解

### 4.1 LED状态显示

#### 实时状态信息
```
--- LEDs ---
G1: ON (Gy:200,Gn:220,Rd:45)
G2: OFF (Gy:120,Gn:140,Rd:30)
R1: ON (Gy:180,Gn:85,Rd:200)
R2: OFF (Gy:100,Gn:45,Rd:120)
G Th: G(g/G)=160.0,Gn(v/V)=180.0
R Th: G(y/Y)=160.0,Rd(r/R)=100.0
```

#### 信息解读
- **LED标识**: G1-G4（绿色LED），R1-R2（红色LED）
- **状态**: ON（亮）/ OFF（灭）
- **数值**: Gy（灰度值），Gn（绿色通道值），Rd（红色通道值）
- **阈值**: 当前使用的检测阈值

#### 视觉反馈
- **LED ROI边框**:
  - 绿色LED亮起: 亮绿色边框
  - 绿色LED熄灭: 暗绿色边框
  - 红色LED亮起: 亮红色边框
  - 红色LED熄灭: 暗红色边框

### 4.2 数码管状态显示

#### 实时识别信息
```
--- Digits ---
D1: 8 [OK]
D2: 8 [OK]
D Th (+/-): 50.0
```

#### 信息解读
- **数码管标识**: D1（左侧），D2（右侧）
- **识别字符**: 0-9 或 ? (无法识别)
- **状态**: OK（正常）/ FAIL（段缺失）/ Unknown（无法识别）
- **阈值**: 当前亮度判断阈值

#### 段状态显示
- **正常段**: 绿色边框，粗线条
- **关闭段**: 灰色边框，细线条
- **缺失段**: 黄色边框 + 红色叉号标记

### 4.3 系统状态信息

#### 右上角信息
```
Mode: Detection (State: CALIB_STATE_START)
FPS: 28.5
```

#### 底部提示信息
- **状态消息**: 当前操作状态和结果反馈
- **操作提示**: 可用的按键操作和功能说明

---

## 高级功能详解

### 5.1 WASD微调系统 🆕

#### 功能概述
在ROI选择和编辑过程中，提供像素级精确调整功能。

#### 操作方式
| 按键组合 | 功能 | 步长 | 说明 |
|----------|------|------|------|
| `W` / `S` | 上下移动 | 1像素 | 精确定位 |
| `A` / `D` | 左右移动 | 1像素 | 精确定位 |
| `Shift` + WASD | 大步长移动 | 10像素 | 快速调整 |
| `Q` / `E` | 调整宽度 | ±1像素 | 尺寸微调 |
| `Z` / `C` | 调整高度 | ±1像素 | 尺寸微调 |

#### 视觉反馈
- **步长显示**: 右上角显示当前调整步长
- **坐标显示**: 实时显示ROI坐标和尺寸
- **边界限制**: 防止ROI超出图像边界

### 5.2 模板复制系统 🆕

#### 使用场景
- 多个相同尺寸的LED需要快速选择
- 规律排列的检测目标
- 提高ROI选择效率

#### 操作流程
1. **选择第一个ROI**: 手动拖拽选择标准ROI
2. **激活模板模式**: 按 `T` 进入模板复制模式
3. **连续复制**:
   - 鼠标移动显示模板预览
   - 点击位置快速放置相同尺寸ROI
   - `Enter` 确认并继续复制
4. **退出模板**: `Esc` 退出模板模式

#### 最佳实践
- 选择最标准、最清晰的LED作为模板
- 模板模式适用于相似目标，特殊情况退出模板单独处理
- 利用模板模式提高批量选择效率

### 5.3 配置管理系统

#### 配置文件类型
- **完整配置**: `combined_config.json` - 包含所有设置
- **LED专用配置**: 快速保存LED样本和阈值
- **备份机制**: 自动备份重要配置

#### 保存策略
| 操作 | 保存内容 | 使用场景 |
|------|----------|----------|
| 校准模式 `S` | 完整配置 | 完成校准后保存 |
| 检测模式 `S` | 所有当前阈值 | 实时优化后保存 |
| 检测模式 `L` | 仅LED参数 | 快速更新LED设置 |

---

## 故障排除

### 6.1 常见问题及解决方案

#### 摄像头相关问题

**问题**: 摄像头无法初始化
- **检查**: 摄像头连接是否正常
- **解决**: 确认摄像头未被其他程序占用
- **验证**: 在设备管理器中查看摄像头状态

**问题**: 图像质量差
- **调整**: 重新设置曝光和亮度参数
- **环境**: 检查光照条件是否稳定
- **硬件**: 清洁摄像头镜头

#### ROI选择问题

**问题**: ROI选择无效
- **尺寸**: 确保ROI尺寸大于最小值(5x5像素)
- **位置**: 检查ROI是否在图像有效区域内
- **操作**: 使用WASD微调功能精确调整

**问题**: 模板复制不准确
- **模板**: 重新选择更标准的模板ROI
- **退出**: 使用Esc退出模板模式，手动调整特殊情况
- **微调**: 使用编辑模式后期调整位置

#### 检测效果问题

**问题**: LED检测不准确
- **阈值**: 在检测模式下实时调整阈值参数
- **样本**: 使用快速重新采样功能更新检测参数
- **环境**: 检查光照条件是否发生变化

**问题**: 数码管识别错误
- **阈值**: 调整数码管亮度阈值
- **ROI**: 重新校准数码管和段ROI
- **背景**: 重新捕捉背景图像

#### 基准点对齐问题

**问题**: 基准点对齐失败
- **特征**: 重新选择更明显的特征点
- **清洁**: 确保基准点区域清洁无遮挡
- **距离**: 检查产品位置偏移是否过大

**问题**: 对齐后检测效果差
- **验证**: 使用 `B` 键临时禁用对齐功能对比
- **重新校准**: 重新选择基准点
- **ROI更新**: 在编辑模式下调整ROI位置

### 6.2 配置文件问题

#### 配置文件损坏
- **症状**: 程序启动异常或参数丢失
- **解决**: 删除 `combined_config.json` 文件重新校准
- **预防**: 定期备份重要配置文件

#### 参数不匹配
- **症状**: 检测效果突然变差
- **检查**: 确认摄像头参数和环境是否发生变化
- **解决**: 重新校准相关参数

### 6.3 性能优化

#### 提高检测速度
- **分辨率**: 根据精度需求选择合适分辨率
- **ROI数量**: 减少不必要的检测区域
- **阈值**: 优化阈值参数减少误判

#### 提高检测精度
- **ROI精度**: 使用WASD微调精确定位ROI
- **样本质量**: 确保样本采集时LED状态明确
- **环境稳定**: 保持稳定的光照条件

---

## 最佳实践指南

### 7.1 校准最佳实践

#### 环境准备
1. **光照稳定**: 确保校准和检测时光照条件一致
2. **产品固定**: 校准时产品位置要稳定
3. **清洁环境**: 保持摄像头和产品表面清洁

#### 基准点选择策略
1. **特征明显**: 选择对比度高、边缘清晰的特征
2. **位置稳定**: 选择不会变化的固定特征点
3. **距离适当**: 两个基准点间距离建议大于100像素
4. **避免干扰**: 避开可能被遮挡或污染的区域

#### LED校准技巧
1. **ROI大小**:
   - 包含完整LED区域
   - 避免包含过多背景
   - 建议比LED实际尺寸大20%
2. **模板使用**:
   - 选择最标准的LED作为模板
   - 相似LED使用模板，特殊情况手动调整
3. **样本采集**:
   - 确保LED状态完全切换
   - 保持稳定光照
   - 避免手动遮挡

#### 数码管校准技巧
1. **"88"图像**: 确保所有段都清晰可见
2. **段ROI选择**: 精确框选每个段，避免重叠
3. **背景图像**: 确保所有段都完全关闭
4. **阈值设置**: 从较低值开始逐步调整

### 7.2 检测优化策略

#### 阈值调整策略
1. **LED阈值**:
   - 先调整灰度阈值(g/G, y/Y)
   - 再微调颜色通道阈值(v/V, r/R)
   - 实时观察检测效果
2. **数码管阈值**:
   - 从背景亮度开始向上调整
   - 确保所有关闭段都被正确识别

#### 环境适应
1. **光照变化**: 使用快速重新采样功能
2. **位置偏移**: 启用基准点自动对齐
3. **定期维护**: 定期检查和更新配置

### 7.3 自动化分析优化

#### "88"触发流程优化
1. **触发时机**: 确保"88"显示稳定后再触发
2. **记录时长**: 根据实际需求调整记录时长
3. **环境稳定**: 分析过程中保持环境稳定

#### 结果验证
1. **日志检查**: 定期检查分析日志的完整性
2. **结果对比**: 对比自动分析和人工检查结果
3. **参数调优**: 根据分析结果调整检测参数

---

## 快捷键速查表

### 摄像头设置模式
| 按键 | 功能 | 调整幅度 | 备注 |
|------|------|----------|------|
| `T` / `t` | 分辨率切换 | 循环切换 | 大写/小写方向不同 |
| `E` / `e` | 曝光调整 | ±1.0 | 大写增加，小写减少 |
| `B` / `b` | 亮度调整 | ±10.0 | 大写增加，小写减少 |
| `S` | 保存设置 | - | 保存摄像头参数 |
| `Enter` | 进入校准 | - | 确认设置 |
| `Q` | 退出程序 | - | 安全退出 |

### 校准主菜单
| 按键 | 功能 | 前置条件 | 备注 |
|------|------|----------|------|
| `B` | 基准点校准 | 无 | 🆕 自动对齐功能 |
| `L` | LED校准 | 无 | 完整LED校准流程 |
| `D` | 数码管校准 | 无 | 完整数码管校准流程 |
| `E` | 编辑LEDs | 已完成LED校准 | 🆕 调整ROI位置 |
| `R` | 重新采样LEDs | 已完成LED校准 | 🆕 快速重新采样 |
| `S` | 保存并退出 | 无 | 保存配置 |
| `Enter` | 进入检测模式 | 校准完整 | 开始检测 |

### 基准点选择
| 按键 | 功能 | 说明 |
|------|------|------|
| **鼠标点击** | 选择基准点 | 点击特征点 |
| `R` | 重新选择 | 清空已选择的基准点 |
| `Esc` | 跳过基准点功能 | 使用固定坐标模式 |
| `Enter` | 完成选择 | 需要选择2个基准点 |

### LED ROI选择
| 按键 | 功能 | 说明 |
|------|------|------|
| **鼠标拖拽** | 选择ROI | 创建矩形区域 |
| `Enter` | 确认ROI | 保存当前ROI |
| `N` | 跳过ROI | 该位置不设置检测 |
| `B` | 回退ROI | 🆕 重新选择上一个 |
| `T` | 模板模式 | 🆕 复制前一个ROI尺寸 |
| `R` | 重置全部 | 清空所有ROI |
| `Esc` | 返回上级 | 退出ROI选择 |

### WASD微调 🆕
| 按键 | 功能 | 步长 | 说明 |
|------|------|------|------|
| `W` / `S` | 上下移动 | 1像素 | 精确定位 |
| `A` / `D` | 左右移动 | 1像素 | 精确定位 |
| `Shift` + WASD | 大步长移动 | 10像素 | 快速调整 |
| `Q` / `E` | 调整宽度 | ±1像素 | 尺寸微调 |
| `Z` / `C` | 调整高度 | ±1像素 | 尺寸微调 |

### LED编辑模式 🆕
| 按键 | 功能 | 说明 |
|------|------|------|
| `1`-`6` | 选择ROI | 数字键快速选择 |
| **鼠标点击** | 选择ROI | 点击ROI选择 |
| **鼠标拖拽** | 移动ROI | 拖拽到新位置 |
| **WASD** | 精确调整 | 像素级微调 |
| `Enter` | 保存更改 | 保存所有修改 |
| `Esc` | 退出编辑 | 不保存更改 |

### 样本采集
| 按键 | 功能 | 说明 |
|------|------|------|
| `C` | 采集样本 | 采集当前状态样本 |
| `A` | 分析样本 | 计算阈值 |
| `Esc` | 返回上一步 | 重新采集或调整 |

### 数码管校准
| 按键 | 功能 | 说明 |
|------|------|------|
| `C` | 捕捉图像 | 捕捉"88"或背景图像 |
| **鼠标拖拽** | 选择ROI | 选择数码管或段区域 |
| `Enter` | 确认选择 | 进入下一步 |
| `N` | 跳过段 | 该段不参与检测 |
| `P` | 返回上一段 | 重新选择上一个段 |
| `+` / `-` | 调整阈值 | ±1.0 |
| `Esc` | 返回上一步 | 重新选择或捕捉 |

### 检测模式
| 按键 | 功能 | 调整幅度 | 说明 |
|------|------|----------|------|
| `g` / `G` | 绿LED灰度阈值 | ±5.0 | 小写减少，大写增加 |
| `v` / `V` | 绿LED绿色阈值 | ±5.0 | 小写减少，大写增加 |
| `y` / `Y` | 红LED灰度阈值 | ±5.0 | 小写减少，大写增加 |
| `r` / `R` | 红LED红色阈值 | ±5.0 | 小写减少，大写增加 |
| `+` / `-` | 数码管亮度阈值 | ±1.0 | 增加/减少 |
| `S` | 保存所有阈值 | - | 🆕 完整保存 |
| `L` | 保存LED参数 | - | 🆕 快速保存 |
| `B` | 切换基准点对齐 | - | 🆕 启用/禁用对齐 |
| `C` | 返回校准模式 | - | 重新校准 |
| `Q` | 退出程序 | - | 安全退出 |

---

## 配置文件详解

### combined_config.json 结构

```json
{
  "camera": {
    "resolution_width": 1920,
    "resolution_height": 1080,
    "exposure": -6.0,
    "brightness": 0.0
  },
  "led": {
    "num_green": 4,
    "num_red": 2,
    "rois": [
      [100, 100, 50, 50],  // G1: [x, y, width, height]
      [200, 100, 50, 50],  // G2
      [300, 100, 50, 50],  // G3
      [400, 100, 50, 50],  // G4
      [100, 200, 50, 50],  // R1
      [200, 200, 50, 50]   // R2
    ],
    "gray_threshold_green": 160.0,
    "green_threshold": 180.0,
    "gray_threshold_red": 160.0,
    "red_threshold": 100.0,
    "off_state_samples": [...],  // 灭灯样本数据
    "on_state_samples": [...]    // 亮灯样本数据
  },
  "digit": {
    "rois": [
      [500, 300, 80, 120],  // Digit 1
      [600, 300, 80, 120]   // Digit 2
    ],
    "segment_rois": [
      [  // Digit 1 的7个段
        [510, 310, 60, 10],  // 段a
        [560, 320, 10, 40],  // 段b
        [560, 370, 10, 40],  // 段c
        [510, 400, 60, 10],  // 段d
        [500, 370, 10, 40],  // 段e
        [500, 320, 10, 40],  // 段f
        [510, 355, 60, 10]   // 段g
      ],
      [...]  // Digit 2 的7个段
    ],
    "brightness_threshold": 50.0
  },
  "base_points": {
    "enabled": true,
    "points": [
      [150, 150],  // 基准点1
      [750, 450]   // 基准点2
    ],
    "templates": [...],  // 基准点模板数据
    "original_led_rois": [...],     // 原始LED ROI坐标
    "original_digit_rois": [...]    // 原始数码管ROI坐标
  }
}
```

### 配置参数说明

#### 摄像头参数
- **resolution_width/height**: 摄像头分辨率
- **exposure**: 曝光值，负值表示较短曝光时间
- **brightness**: 亮度值，0为默认亮度

#### LED参数
- **num_green/red**: 绿色/红色LED数量
- **rois**: LED ROI坐标数组，格式为[x, y, width, height]
- **阈值参数**: 各种检测阈值
- **样本数据**: 校准时采集的亮灭状态样本

#### 数码管参数
- **rois**: 数码管整体ROI坐标
- **segment_rois**: 每个数码管的7个段ROI坐标
- **brightness_threshold**: 段亮灭判断阈值

#### 基准点参数 🆕
- **enabled**: 是否启用基准点对齐
- **points**: 基准点坐标
- **templates**: 基准点模板数据
- **original_*_rois**: 原始ROI坐标，用于对齐计算

---

## 版本更新记录

### v2.0 新增功能 🆕

#### 基准点自动对齐系统
- ✅ 基准点选择和校准
- ✅ 实时ROI坐标自动调整
- ✅ 对齐状态显示和控制
- ✅ 基准点配置验证

#### LED校准增强
- ✅ WASD微调功能
- ✅ 模板复制功能
- ✅ ROI编辑模式
- ✅ 快速重新采样
- ✅ 回退功能

#### 检测模式优化
- ✅ 实时阈值调整
- ✅ 快速保存功能
- ✅ 基准点对齐控制
- ✅ "88"触发自动化分析流程

#### 用户体验改进
- ✅ 丰富的视觉反馈
- ✅ 详细的操作提示
- ✅ 状态机流程显示
- ✅ 错误处理和恢复

### v1.0 基础功能
- ✅ 三阶段工作流程
- ✅ LED和数码管检测
- ✅ 配置文件管理
- ✅ 基本ROI选择

---

## 技术支持

### 常见问题解答

**Q: 基准点对齐失败怎么办？**
A: 检查基准点是否清晰可见，重新选择对比度更高的特征点，或临时禁用对齐功能。

**Q: 模板复制的ROI位置不准确？**
A: 退出模板模式，使用编辑功能手动调整，或重新选择更标准的模板ROI。

**Q: "88"分析流程中断怎么办？**
A: 检查日志文件权限，确保分析脚本正确导入，重新触发"88"开始新的分析流程。

**Q: 检测效果突然变差？**
A: 检查光照条件是否变化，使用快速重新采样功能更新检测参数，或重新校准相关组件。

**Q: WASD微调不响应？**
A: 确保当前有选中的ROI（蓝色边框），在ROI选择或编辑模式下才能使用微调功能。

**Q: 模板模式下无法退出？**
A: 按 `Esc` 键退出模板模式，或按 `Enter` 确认当前ROI后再退出。

**Q: 配置文件损坏如何恢复？**
A: 删除 `combined_config.json` 文件，重新启动程序进行完整校准。

### 性能优化建议

#### 硬件优化
1. **摄像头选择**: 使用高质量工业摄像头
2. **光源稳定**: 使用LED环形光源或条形光源
3. **固定支架**: 确保摄像头和产品位置稳定

#### 软件优化
1. **分辨率平衡**: 根据检测精度需求选择合适分辨率
2. **ROI优化**: 精确设置ROI大小，避免包含过多背景
3. **阈值调优**: 定期根据环境变化调整检测阈值

#### 环境优化
1. **光照控制**: 使用稳定的人工光源，避免自然光干扰
2. **背景处理**: 使用单色背景，提高检测对比度
3. **防震措施**: 减少设备振动对检测精度的影响

### 联系方式
如遇到技术问题，请提供以下信息：
- 系统版本信息
- 错误现象描述
- 配置文件内容
- 操作步骤记录
- 环境光照条件

### 更新计划
- **v2.1**: 增加多产品类型支持
- **v2.2**: 优化检测算法性能
- **v2.3**: 增加远程监控功能
- **v3.0**: 集成AI智能识别

---

## 附录

### A. 术语表

| 术语 | 说明 |
|------|------|
| **ROI** | Region of Interest，感兴趣区域，检测的目标区域 |
| **基准点** | 用于自动对齐的固定特征点 |
| **模板复制** | 使用已选择ROI的尺寸快速创建相同大小的ROI |
| **阈值** | 用于区分目标状态的判断标准值 |
| **样本采集** | 收集LED亮灭状态的颜色数据用于阈值计算 |
| **7段显示器** | 数码管的显示结构，包含a-g共7个段 |
| **状态机** | "88"触发分析流程的各个处理状态 |

### B. 错误代码表

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| **CAM_001** | 摄像头初始化失败 | 检查摄像头连接和驱动 |
| **ROI_002** | ROI尺寸过小 | 确保ROI大于5x5像素 |
| **BASE_003** | 基准点对齐失败 | 重新选择基准点或禁用对齐 |
| **SAMPLE_004** | 样本采集失败 | 检查LED状态和光照条件 |
| **CONFIG_005** | 配置文件损坏 | 删除配置文件重新校准 |
| **ANALYSIS_006** | 分析脚本错误 | 检查分析脚本导入和权限 |

### C. 快速入门检查清单

#### 首次使用
- [ ] 确认摄像头连接正常
- [ ] 调整摄像头参数获得清晰图像
- [ ] 选择基准点（可选）
- [ ] 完成LED校准（ROI选择→样本采集→阈值分析）
- [ ] 完成数码管校准（"88"图像→ROI选择→段选择→背景图像→阈值调整）
- [ ] 保存配置并进入检测模式

#### 日常使用
- [ ] 检查基准点对齐状态
- [ ] 观察LED和数码管检测效果
- [ ] 根据需要调整检测阈值
- [ ] 定期保存优化后的配置

#### 故障排除
- [ ] 检查摄像头和光照条件
- [ ] 验证ROI位置是否正确
- [ ] 确认检测阈值是否合适
- [ ] 检查配置文件完整性

---

**版本**: v2.0
**更新日期**: 2025-01-13
**文档状态**: 完整版
**适用系统**: LED数码管检测系统 v2.0+

*本文档涵盖了系统的所有功能和操作方法，建议按章节顺序学习使用。如有疑问请参考故障排除章节或联系技术支持。*

---

**© 2025 LED数码管检测系统开发团队 | 保留所有权利**
