#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析G33、R1、R2的ON时间
"""

import re
import os
from datetime import datetime

def parse_timestamp(timestamp_str):
    """解析时间戳字符串为datetime对象"""
    try:
        return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
    except ValueError:
        return None

def calculate_time_diff_seconds(start_time_str, end_time_str):
    """计算两个时间戳之间的秒数差"""
    start_dt = parse_timestamp(start_time_str)
    end_dt = parse_timestamp(end_time_str)
    if start_dt and end_dt:
        return (end_dt - start_dt).total_seconds()
    return 0.0

def analyze_special_led_times():
    """分析G33、R1、R2的ON时间"""
    log_file = 'led_digit_detection.log'
    
    if not os.path.exists(log_file):
        print(f"文件 {log_file} 不存在!")
        return
    
    print("="*60)
    print("G33、R1、R2 ON时间专项分析")
    print("="*60)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(log_file, 'r', encoding='latin-1') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return
    
    print(f"文件大小: {len(content)/1024:.2f} KB")
    
    # 正则表达式模式
    timestamp_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})')
    
    # G33状态变化事件
    g33_state_pattern = re.compile(r'INFO - LED G33 state (\w+)->(\w+)')
    # R1状态变化事件  
    r1_state_pattern = re.compile(r'INFO - LED R1 state (\w+)->(\w+)')
    # R2状态变化事件
    r2_state_pattern = re.compile(r'INFO - LED R2 state (\w+)->(\w+)')
    
    def extract_state_changes(pattern, led_name):
        """提取LED状态变化事件"""
        events = []
        for match in pattern.finditer(content):
            # 获取包含这个匹配的行
            line_start = content.rfind('\n', 0, match.start()) + 1
            line_end = content.find('\n', match.start())
            if line_end == -1:
                line_end = len(content)
            line = content[line_start:line_end]
            
            # 提取时间戳
            timestamp_match = timestamp_pattern.search(line)
            timestamp = timestamp_match.group(1) if timestamp_match else "unknown"
            
            from_state = match.group(1)
            to_state = match.group(2)
            events.append((timestamp, from_state, to_state))
        
        print(f"\n{led_name} 状态变化事件:")
        for i, (ts, from_s, to_s) in enumerate(events, 1):
            print(f"  {i}. {ts}: {from_s} -> {to_s}")
        
        return events
    
    def calculate_on_duration(events, led_name):
        """计算ON状态持续时间"""
        on_periods = []
        current_on_start = None
        total_duration = 0.0
        
        print(f"\n{led_name} ON时间段分析:")
        
        for timestamp, from_state, to_state in events:
            if to_state == "ON" and current_on_start is None:
                current_on_start = timestamp
                print(f"  开始ON: {timestamp}")
            elif from_state == "ON" and to_state == "OFF" and current_on_start is not None:
                duration = calculate_time_diff_seconds(current_on_start, timestamp)
                on_periods.append((current_on_start, timestamp, duration))
                total_duration += duration
                print(f"  结束ON: {timestamp} (持续 {duration:.3f}秒)")
                current_on_start = None
        
        # 如果最后还在ON状态，需要特殊处理
        if current_on_start is not None:
            print(f"  注意: {led_name} 在日志结束时仍为ON状态，开始时间: {current_on_start}")
            # 尝试找到最后一个时间戳作为结束时间
            last_timestamp = None
            for match in timestamp_pattern.finditer(content):
                last_timestamp = match.group(1)
            
            if last_timestamp:
                duration = calculate_time_diff_seconds(current_on_start, last_timestamp)
                on_periods.append((current_on_start, last_timestamp, duration))
                total_duration += duration
                print(f"  估算结束: {last_timestamp} (持续 {duration:.3f}秒)")
        
        print(f"\n{led_name} 总结:")
        print(f"  ON次数: {len(on_periods)}")
        print(f"  总ON时间: {total_duration:.3f}秒")
        print(f"  状态: {'好' if total_duration > 1.0 else '不好'} (需要>1秒)")
        
        if on_periods:
            print(f"  详细时间段:")
            for i, (start, end, duration) in enumerate(on_periods, 1):
                print(f"    {i}. {start} 到 {end} ({duration:.3f}秒)")
        
        return total_duration, len(on_periods)
    
    # 分析G33
    print("\n" + "="*40)
    print("G33 分析")
    print("="*40)
    g33_events = extract_state_changes(g33_state_pattern, "G33")
    g33_duration, g33_count = calculate_on_duration(g33_events, "G33")
    
    # 分析R1
    print("\n" + "="*40)
    print("R1 分析")
    print("="*40)
    r1_events = extract_state_changes(r1_state_pattern, "R1")
    r1_duration, r1_count = calculate_on_duration(r1_events, "R1")
    
    # 分析R2
    print("\n" + "="*40)
    print("R2 分析")
    print("="*40)
    r2_events = extract_state_changes(r2_state_pattern, "R2")
    r2_duration, r2_count = calculate_on_duration(r2_events, "R2")
    
    # 总结
    print("\n" + "="*60)
    print("最终总结")
    print("="*60)
    print(f"G33: {g33_duration:.3f}秒 ({g33_count}次) - {'好' if g33_duration > 1.0 else '不好'}")
    print(f"R1:  {r1_duration:.3f}秒 ({r1_count}次) - {'好' if r1_duration > 1.0 else '不好'}")
    print(f"R2:  {r2_duration:.3f}秒 ({r2_count}次) - {'好' if r2_duration > 1.0 else '不好'}")
    
    all_good = g33_duration > 1.0 and r1_duration > 1.0 and r2_duration > 1.0
    print(f"\n综合状态: {'GOOD' if all_good else 'BAD'}")
    print(f"M13发送值: {1 if all_good else 3}")

if __name__ == "__main__":
    analyze_special_led_times()
