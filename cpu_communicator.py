import requests
from requests.auth import HTTPBasicAuth
import logging

# 定义常量 (建议将 URL 和认证信息移到配置文件或环境变量中以提高安全性)
CPU_URL = "http://*************:8090/cgi-bin/memory"
CPU_AUTH = HTTPBasicAuth('admin', 'admin')

def send_value_to_cpu(value: int, address: int) -> bool:
    """
    向指定的 CPU 地址发送数值。

    Args:
        value: 要写入的整数值。
        address: 目标 M 区地址 (整数)。

    Returns:
        True 如果发送成功 (HTTP 状态码 200)，否则 False。
    """
    payload = {
        "address": address,
        "value": value,
        "addressType": 2,  # 2 代表 M 区
        "startIndex": {}
    }

    try:
        response = requests.post(
            CPU_URL,
            params={'key': 'memory'},
            json=payload,
            auth=CPU_AUTH,
            headers={'Cache-Control': 'no-cache'},
            timeout=5  # 添加超时设置，防止无限等待 (例如 5 秒)
        )

        # 检查响应状态码
        if response.status_code == 200:
            try:
                json_data = response.json()
                logging.info(f"成功发送 value={value} 到 address={address}. 响应: {json_data}")
                return True
            except requests.exceptions.JSONDecodeError:
                logging.warning(f"发送 value={value} 到 address={address} 成功 (200 OK)，但无法解析 JSON 响应: {response.text}")
                return True
        else:
            error_msg = f"发送 value={value} 到 address={address} 失败。状态码: {response.status_code}, 响应: {response.text}"
            logging.error(error_msg)
            print(error_msg)
            return False

    except requests.exceptions.RequestException as e:
        error_msg = f"发送 value={value} 到 address={address} 时发生网络错误: {e}"
        logging.error(error_msg)
        print(error_msg)
        return False

if __name__ == '__main__':
    # 用于测试的简单示例
    logging.basicConfig(level=logging.INFO)
    print("测试发送...")
    # 测试发送 3 到地址 10
    # success = send_value_to_cpu(3, 10)
    # print(f"发送 3 到 10 结果: {success}")

    # 测试发送 1 到地址 11
    # success_ip = send_value_to_cpu(1, 11)
    # print(f"发送 1 到 11 结果: {success_ip}")
    print("测试代码已注释掉，请取消注释并确保网络可达以进行实际测试。") 