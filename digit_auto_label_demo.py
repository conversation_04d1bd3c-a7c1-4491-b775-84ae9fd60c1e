import cv2
import numpy as np
import argparse
import os

# ------------------------- 预处理与工具函数 -------------------------

def preprocess(gray: np.ndarray) -> np.ndarray:
    """灰度图 → 二值图。数字亮、背景暗，返回白=亮段的二值图"""
    blur = cv2.GaussianBlur(gray, (5, 5), 0)

    # 1️⃣ 先尝试保持亮段 = 白色的普通 Otsu
    _, bin1 = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

    # 2️⃣ 统计连通域数量，若太少(可能阈值方向相反)则尝试反色版本
    cnts1, _ = cv2.findContours(bin1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if len(cnts1) < 5:  # 经验阈值：7 段应不少于 5 个连通域
        _, bin1 = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)

    # 去小噪声
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    bin_img = cv2.morphologyEx(bin1, cv2.MORPH_OPEN, kernel, iterations=1)
    return bin_img


def estimate_skew(bin_img: np.ndarray, min_area: int = 200) -> float:
    """估计竖向段的平均倾角 (度)。正值≈逆时针。"""
    contours, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    angles = []
    for cnt in contours:
        if cv2.contourArea(cnt) < min_area:
            continue
        rect = cv2.minAreaRect(cnt)  # ((cx,cy),(w,h),theta)
        (w, h) = rect[1]
        if w == 0 or h == 0:
            continue
        # 只看竖向段 (h> w)
        if h > w:
            angle = rect[2]  # (-90,0]
            if angle < -45:
                angle += 90  # 转成 (-45,45]
            angles.append(angle)
    return float(np.mean(angles)) if angles else 0.0


def rotate(img: np.ndarray, angle: float) -> np.ndarray:
    """按给定角度旋转图像（保持原尺寸）。"""
    h, w = img.shape[:2]
    center = (w // 2, h // 2)
    M = cv2.getRotationMatrix2D(center, angle, 1.0)
    return cv2.warpAffine(img, M, (w, h), flags=cv2.INTER_LINEAR, borderValue=0)


def combine_bboxes(boxes):
    if not boxes:
        return None
    xs = [b[0] for b in boxes]
    ys = [b[1] for b in boxes]
    x2 = [b[0] + b[2] for b in boxes]
    y2 = [b[1] + b[3] for b in boxes]
    return (int(min(xs)), int(min(ys)), int(max(x2) - min(xs)), int(max(y2) - min(ys)))


# --------------------------- 主检测流程 ---------------------------

def detect_digit_rois(gray: np.ndarray):
    """返回 (digit1_roi, digit2_roi, segment_boxes, used_angle)。"""
    bin_img = preprocess(gray)

    # 检测并校正倾斜
    skew = estimate_skew(bin_img)
    used_angle = 0.0
    if abs(skew) > 3:  # 阈值可调
        used_angle = -skew
        bin_img = preprocess(rotate(gray, used_angle))
        print(f"Detected skew {skew:.2f}°, image rotated {used_angle:.2f}° for processing")
    else:
        print(f"Skew {skew:.2f}° within tolerance, no rotation applied")

    contours, _ = cv2.findContours(bin_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    bboxes = []
    centers_x = []
    for cnt in contours:
        x, y, w, h = cv2.boundingRect(cnt)
        if w * h < 150:  # 降低面积阈值
            continue
        bboxes.append((x, y, w, h))
        centers_x.append(x + w / 2)

    if len(bboxes) < 2:
        return None, None, [], 0.0

    # 按中心 x 坐标分两簇
    median_x = np.median(centers_x)
    left_boxes = [b for b, cx in zip(bboxes, centers_x) if cx <= median_x]
    right_boxes = [b for b, cx in zip(bboxes, centers_x) if cx > median_x]

    digit1_roi = combine_bboxes(left_boxes)
    digit2_roi = combine_bboxes(right_boxes)

    return digit1_roi, digit2_roi, bboxes, used_angle


# ----------------------------- CLI -----------------------------

def main():
    parser = argparse.ArgumentParser(description="Offline demo: auto label two 7-segment digits in an '88' image")
    parser.add_argument("image", help="Path to source image (should display '88')")
    parser.add_argument("-o", "--output", help="Path to save visual result (optional)")
    args = parser.parse_args()

    src = cv2.imread(args.image)
    if src is None:
        print("Error: cannot load image", args.image)
        return

    gray = cv2.cvtColor(src, cv2.COLOR_BGR2GRAY)
    d1, d2, seg_boxes, used_angle = detect_digit_rois(gray)

    vis = src.copy()
    if abs(used_angle) > 0.1:
        vis = rotate(vis, used_angle)

    # 绘制 Digit ROI
    for idx, roi in enumerate([d1, d2]):
        if roi is None:
            continue
        x, y, w, h = roi
        color = (0, 255, 0) if idx == 0 else (255, 0, 0)
        cv2.rectangle(vis, (x, y), (x + w, y + h), color, 2)
        cv2.putText(vis, f"D{idx+1}", (x, y - 8), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

    # 绘制所有段的连通域框
    for (x, y, w, h) in seg_boxes:
        cv2.rectangle(vis, (x, y), (x + w, y + h), (255, 255, 255), 1)

    # 左上角叠一张 bin 预览
    bin_preview = preprocess(gray)
    ph, pw = bin_preview.shape
    preview_small = cv2.resize(bin_preview, (pw // 4, ph // 4))
    if vis.shape[0] >= preview_small.shape[0] and vis.shape[1] >= preview_small.shape[1]:
        vis[0:preview_small.shape[0], 0:preview_small.shape[1]] = cv2.cvtColor(preview_small, cv2.COLOR_GRAY2BGR)

    if args.output:
        cv2.imwrite(args.output, vis)
        print("Result saved to", args.output)

    cv2.imshow("Auto-Label Demo", vis)
    cv2.waitKey(0)
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main() 