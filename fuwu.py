#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络设备信息读取服务
用于获取指定设备的网络配置信息
"""

import requests
import json
import logging
import socket
import threading
import time
from requests.auth import HTTPBasicAuth
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NetworkInfoReader:
    """网络信息读取器"""
    
    def __init__(self):
        self.url = "http://192.168.20.80:8090/cgi-bin/netinfo?key=eth0"
        self.username = "admin"
        self.password = "admin"
        self.timeout = 10  # 请求超时时间（秒）
    
    def read_network_info(self) -> Dict[str, Any]:
        """
        读取网络设备信息
        
        Returns:
            Dict[str, Any]: 包含网络信息的字典
        """
        try:
            logger.info(f"开始读取网络信息，URL: {self.url}")
            
            # 发送HTTP请求
            response = requests.get(
                self.url,
                auth=HTTPBasicAuth(self.username, self.password),
                timeout=self.timeout,
                verify=False  # 忽略SSL证书验证
            )
            
            # 检查响应状态
            if response.status_code == 200:
                # 解析JSON响应
                data = response.json()
                logger.info(f"成功获取网络信息: {data}")
                
                if not data.get('error', True) and 'data' in data:
                    eth0_info = data['data'].get('eth0', {})
                    return {
                        'success': True,
                        'data': {
                            'eth0ipV4': eth0_info.get('eth0ipV4', ''),
                            'eth0netmask': eth0_info.get('eth0netmask', ''),
                            'eth0gate': eth0_info.get('eth0gate', ''),
                            'eth0mac': eth0_info.get('eth0mac', '')
                        }
                    }
                else:
                    logger.error(f"设备返回错误: {data}")
                    return {
                        'success': False,
                        'message': '设备返回错误信息'
                    }
            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return {
                    'success': False,
                    'message': f'HTTP请求失败，状态码: {response.status_code}'
                }
                
        except requests.exceptions.Timeout:
            logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请检查设备连接'
            }
        except requests.exceptions.ConnectionError:
            logger.error("连接错误")
            return {
                'success': False,
                'message': '无法连接到设备，请检查网络连接'
            }
        except json.JSONDecodeError:
            logger.error("JSON解析错误")
            return {
                'success': False,
                'message': '设备响应格式错误'
            }
        except Exception as e:
            logger.error(f"读取网络信息时发生未知错误: {str(e)}")
            return {
                'success': False,
                'message': f'读取失败: {str(e)}'
            }

class SocketServer:
    """Socket服务器，用于与前端通信"""
    
    def __init__(self, host='localhost', port=9999):
        self.host = host
        self.port = port
        self.server_socket = None
        self.running = False
        self.network_reader = NetworkInfoReader()
    
    def start_server(self):
        """启动Socket服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            
            logger.info(f"Socket服务器启动成功，监听 {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    logger.info(f"客户端连接: {address}")
                    
                    # 在新线程中处理客户端请求
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket,)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        logger.error(f"接受连接时发生错误: {e}")
                    
        except Exception as e:
            logger.error(f"启动Socket服务器失败: {e}")
        finally:
            self.stop_server()
    
    def handle_client(self, client_socket):
        """处理客户端请求"""
        try:
            # 接收请求
            request = client_socket.recv(1024).decode('utf-8')
            logger.info(f"收到请求: {request}")
            
            if request.strip() == "READ_NETWORK_INFO":
                # 读取网络信息
                result = self.network_reader.read_network_info()
                response = json.dumps(result, ensure_ascii=False)
            else:
                response = json.dumps({
                    'success': False,
                    'message': '未知请求'
                }, ensure_ascii=False)
            
            # 发送响应
            client_socket.send(response.encode('utf-8'))
            logger.info(f"发送响应: {response}")
            
        except Exception as e:
            logger.error(f"处理客户端请求时发生错误: {e}")
            error_response = json.dumps({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            }, ensure_ascii=False)
            try:
                client_socket.send(error_response.encode('utf-8'))
            except:
                pass
        finally:
            client_socket.close()
    
    def stop_server(self):
        """停止Socket服务器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        logger.info("Socket服务器已停止")

def main():
    """主函数 - 启动Socket服务器"""
    print("正在启动网络信息读取服务...")
    print("Socket服务器将监听 localhost:9999")
    print("按 Ctrl+C 停止服务")

    # 启动Socket服务器
    server = SocketServer()
    try:
        server.start_server()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务器...")
        server.stop_server()
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
