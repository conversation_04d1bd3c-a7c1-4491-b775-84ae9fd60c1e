#!/usr/bin/env python3
"""
第一阶段精度优化单元测试
测试最亮像素采样算法的正确性和性能
"""

import numpy as np
import cv2
import time
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from constants import *
from led_detector import calculate_bright_pixel_average
from digit_detector import calculate_bright_pixel_brightness

def test_bright_pixel_average_basic():
    """测试LED最亮像素采样基本功能"""
    print("测试1: LED最亮像素采样基本功能")
    
    # 创建测试ROI：中心亮，边缘暗
    roi = np.zeros((10, 10, 3), dtype=np.uint8)
    roi[4:6, 4:6] = [100, 200, 50]  # 中心4个像素为绿色LED
    roi[0:2, 0:2] = [30, 40, 35]    # 边角为背景
    roi[8:10, 8:10] = [25, 35, 30]  # 另一边角为背景
    
    # 测试10%采样
    result = calculate_bright_pixel_average(roi, 0.1)
    original = np.mean(roi, axis=(0, 1), dtype=np.float64)
    
    print(f"  原始均值: B={original[0]:.1f}, G={original[1]:.1f}, R={original[2]:.1f}")
    print(f"  最亮10%: B={result[0]:.1f}, G={result[1]:.1f}, R={result[2]:.1f}")
    
    # 验证结果：绿色通道应该显著提高
    assert result[1] > original[1], "绿色通道应该比原始均值更高"
    assert result[1] > 80, "绿色通道应该明显提升"
    
    print("  ✅ 测试通过")
    return True

def test_bright_pixel_brightness_basic():
    """测试数码管最亮像素采样基本功能"""
    print("测试2: 数码管最亮像素采样基本功能")
    
    # 创建测试段：中心亮，边缘暗
    segment = np.zeros((8, 20), dtype=np.uint8)
    segment[3:5, 8:12] = 200  # 中心区域亮
    segment[0:2, 0:4] = 30    # 边缘暗
    segment[6:8, 16:20] = 25  # 另一边缘暗
    
    # 测试10%采样
    result = calculate_bright_pixel_brightness(segment, 0.1)
    original = cv2.mean(segment)[0]
    
    print(f"  原始均值: {original:.1f}")
    print(f"  最亮10%: {result:.1f}")
    
    # 验证结果：应该显著提高
    assert result > original, "最亮像素均值应该比原始均值更高"
    assert result > 100, "最亮像素均值应该明显提升"
    
    print("  ✅ 测试通过")
    return True

def test_edge_cases():
    """测试边界情况"""
    print("测试3: 边界情况处理")
    
    # 测试空ROI
    empty_roi = np.array([]).reshape(0, 0, 3).astype(np.uint8)
    result = calculate_bright_pixel_average(empty_roi)
    assert np.array_equal(result, [0.0, 0.0, 0.0]), "空ROI应该返回零值"
    
    # 测试很小的ROI
    tiny_roi = np.ones((2, 2, 3), dtype=np.uint8) * 100
    result = calculate_bright_pixel_average(tiny_roi, 0.1)
    original = np.mean(tiny_roi, axis=(0, 1), dtype=np.float64)
    assert np.allclose(result, original), "小ROI应该回退到原始方法"
    
    # 测试功能关闭
    normal_roi = np.ones((10, 10, 3), dtype=np.uint8) * 100
    result = calculate_bright_pixel_average(normal_roi, 0.0)  # 关闭功能
    original = np.mean(normal_roi, axis=(0, 1), dtype=np.float64)
    assert np.allclose(result, original), "功能关闭时应该使用原始方法"
    
    print("  ✅ 边界情况测试通过")
    return True

def test_performance():
    """测试性能影响"""
    print("测试4: 性能测试")
    
    # 创建较大的测试ROI
    large_roi = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
    
    # 测试原始方法性能
    start_time = time.perf_counter()
    for _ in range(100):
        original = np.mean(large_roi, axis=(0, 1), dtype=np.float64)
    original_time = time.perf_counter() - start_time
    
    # 测试优化方法性能
    start_time = time.perf_counter()
    for _ in range(100):
        optimized = calculate_bright_pixel_average(large_roi, 0.1)
    optimized_time = time.perf_counter() - start_time
    
    print(f"  原始方法: {original_time*1000:.2f}ms (100次)")
    print(f"  优化方法: {optimized_time*1000:.2f}ms (100次)")
    print(f"  性能比: {optimized_time/original_time:.2f}x")
    
    # 验证性能影响在可接受范围内
    assert optimized_time < original_time * 5, "优化方法耗时不应超过原始方法5倍"
    
    print("  ✅ 性能测试通过")
    return True

def test_consistency():
    """测试一致性：相同输入应该产生相同输出"""
    print("测试5: 一致性测试")
    
    # 创建测试ROI
    roi = np.random.randint(0, 255, (20, 20, 3), dtype=np.uint8)
    
    # 多次运行，验证结果一致性
    results = []
    for _ in range(10):
        result = calculate_bright_pixel_average(roi, 0.1)
        results.append(result)
    
    # 验证所有结果相同
    for i in range(1, len(results)):
        assert np.allclose(results[0], results[i]), f"第{i+1}次结果与第1次不一致"
    
    print("  ✅ 一致性测试通过")
    return True

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("第一阶段精度优化单元测试")
    print("=" * 50)
    
    tests = [
        test_bright_pixel_average_basic,
        test_bright_pixel_brightness_basic,
        test_edge_cases,
        test_performance,
        test_consistency
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    print("=" * 50)
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
