# LED数码管检测系统 - ROI选择操作指南 v2.0

## 概述

本系统是一个基于OpenCV的LED灯珠和数码管检测系统，支持实时检测LED状态和数码管显示内容。系统采用三阶段工作流程：**摄像头设置** → **校准配置** → **实时检测**。

## 系统架构

### 工作模式
- **摄像头设置模式** (`MODE_CAMERA_SETTINGS`): 调整摄像头参数
- **校准模式** (`MODE_CALIBRATION`): 配置ROI区域和检测参数  
- **检测模式** (`MODE_DETECTION`): 实时检测和分析

### 配置文件
- `combined_config.json`: 存储所有配置信息（摄像头参数、ROI坐标、阈值等）

---

## 完整操作流程

### 第一阶段：摄像头设置

**目标**: 调整摄像头参数以获得最佳图像质量

#### 操作步骤：
1. **启动程序** - 自动进入摄像头设置模式
2. **调整参数**：
   - `T`/`t`: 切换分辨率 (640x480 → 1920x1080 等)
   - `E`/`e`: 调整曝光值 (±1.0)
   - `B`/`b`: 调整亮度值 (±10.0)
3. **保存设置**: `S` - 保存当前摄像头参数
4. **进入校准**: `Enter` - 确认设置并进入校准模式

#### 界面提示：
```
Resolution: 1920x1080 ('T'/'t') | Exposure: -6.0 ('E'/'e') | Brightness: 0.0 ('B'/'b')
Press 'S' Save | 'Enter' Calibrate | 'Q' Quit
```

---

### 第二阶段：校准模式

**目标**: 配置LED和数码管的检测区域及参数

#### 2.1 校准主菜单

**操作选项**：
- `L`: LED校准 - 配置LED灯珠检测
- `D`: 数码管校准 - 配置数码管检测  
- `E`: 编辑LEDs - 调整已有LED ROI位置 *(需要先完成LED校准)*
- `S`: 保存并退出校准模式
- `Enter`: 进入检测模式 *(需要校准完整)*

#### 界面提示：
```
Calibration: 'L' LED | 'D' Digit | 'E' Edit LEDs | 'S' Save&Exit Calib | 'Enter' Detect (if ready)
```

---

#### 2.2 LED校准流程

##### 2.2.1 ROI区域选择

**目标**: 为每个LED灯珠定义检测区域

**基础操作**：
- **拖拽选择**: 鼠标左键拖拽选择ROI区域
- **确认**: `Enter` - 确认当前ROI
- **跳过**: `N` - 跳过当前ROI
- **回退**: `B` - 回退到上一个ROI重新选择 ⭐
- **重置**: `R` - 重置所有LED ROI
- **退出**: `Esc` - 返回校准主菜单

**高级功能 - 模板复制** ⭐：
1. **进入模板模式**: `T` - 使用前一个ROI作为模板
2. **连续复制**: 
   - 鼠标移动显示模板预览（黄色虚线框+角标）
   - 点击位置放置相同大小的ROI
   - `Enter` 确认并继续复制下一个
3. **退出模板**: `Esc` - 退出模板模式

**选择顺序**: G1 → G2 → G3 → G4 → R1 → R2 (绿色LED优先)

#### 界面提示：
```
# 常规模式
Drag mouse. 'Enter' Confirm | 'N' Next/Skip | 'B' Back | 'T' Template | 'R' Reset | Esc Back

# 模板模式  
Template Mode: Click to place | 'Enter' Confirm & Continue | 'Esc' Exit Template | 'R' Reset
```

##### 2.2.2 样本采集

**目标**: 采集LED亮灭状态的颜色样本用于阈值计算

**灭灯样本采集**：
1. **确保所有LED熄灭**
2. **采集样本**: `C` - 采集约10帧灭灯样本
3. **继续**: 自动进入亮灯采集

**亮灯样本采集**：
1. **确保所有LED点亮**  
2. **采集样本**: `C` - 采集约10帧亮灯样本
3. **继续**: 自动进入阈值分析

##### 2.2.3 阈值分析

**目标**: 基于采集的样本自动计算最优检测阈值

**操作**：
- **分析**: `A` - 计算并保存阈值
- **完成**: 自动返回校准主菜单

**输出示例**：
```
LED 阈值计算完成: G(G=160.0, Gn=180.0), R(G=160.0, Rd=100.0)
LED 校准完成！可以按 'E' 进入编辑模式调整ROI位置。
```

---

#### 2.3 LED编辑模式 ⭐

**目标**: 微调已校准LED的ROI位置

**进入条件**: 完成LED校准后，在校准主菜单按 `E`

**操作方式**：
1. **选择ROI**:
   - 直接点击ROI区域
   - 按数字键 `1`-`6` 选择对应ROI
2. **移动ROI**: 拖拽到新位置
3. **确认保存**: `Enter` - 保存所有更改
4. **退出编辑**: `Esc` - 退出编辑模式

**视觉反馈**：
- **选中ROI**: 橙色边框 + 黄色角标
- **未选中ROI**: 正常绿色/红色边框
- **数字提示**: 每个ROI中心显示对应数字键

#### 界面提示：
```
# 未选中状态
Click ROI or press '1'-'6' to select | 'Enter' Confirm All | 'Esc' Exit Edit

# 选中状态
Selected: G1 | Drag to move | '1'-'6' Select ROI | 'Enter' Confirm | 'Esc' Exit Edit
```

---

#### 2.4 数码管校准流程

##### 2.4.1 捕捉"88"图像
1. **显示"88"**: 确保数码管显示"88"
2. **捕捉图像**: `C` - 捕捉当前帧作为参考

##### 2.4.2 选择数码管ROI
1. **选择左侧数字区域** (Digit 1)
2. **选择右侧数字区域** (Digit 2)

##### 2.4.3 选择段ROI
为每个数码管的7个段(a-g)分别选择ROI区域

##### 2.4.4 捕捉背景图像
1. **关闭数码管显示**
2. **捕捉背景**: `C` - 捕捉背景图像

##### 2.4.5 调整亮度阈值
- `+`/`=`: 增加亮度阈值
- `-`/`_`: 减少亮度阈值
- `Enter`: 确认阈值设置

---

### 第三阶段：检测模式

**目标**: 实时检测LED状态和数码管显示

#### 3.1 基本操作

**模式切换**：
- `C`: 返回校准模式
- `S`: 保存当前阈值 ⭐
- `Q`: 退出程序

#### 3.2 实时阈值调整 ⭐

**LED阈值调整**：
- `g`/`G`: 绿色LED灰度阈值 (±5.0)
- `v`/`V`: 绿色LED绿色通道阈值 (±5.0)  
- `y`/`Y`: 红色LED灰度阈值 (±5.0)
- `r`/`R`: 红色LED红色通道阈值 (±5.0)

**数码管阈值调整**：
- `+`/`=`: 增加数码管亮度阈值 (±1.0)
- `-`/`_`: 减少数码管亮度阈值 (±1.0)

**保存调整**: `S` - 保存当前所有阈值到配置文件

#### 3.3 显示信息

**LED状态显示**：
```
--- LEDs ---
G1: ON (Gy:200,Gn:220,Rd:45)
G2: OFF (Gy:120,Gn:140,Rd:30)
...
G Th: G(g/G)=160.0,Gn(v/V)=180.0
R Th: G(y/Y)=160.0,Rd(r/R)=100.0
```

**数码管状态显示**：
```
--- Digits ---  
D1: 8 [OK]
D2: 8 [OK]
D Th (+/-): 50.0
```

#### 界面提示：
```
MODE: Detection | 'C' Calibrate | 'S' Save Thresh | 'Q' Quit
```

---

## 新增功能特性 ⭐

### 1. 回退功能
- **位置**: LED ROI选择过程中
- **操作**: `B` 键回退到上一个ROI
- **优势**: 无需重置全部，可单独修改错误的ROI

### 2. 模板复制功能  
- **位置**: LED ROI选择过程中
- **操作**: `T` 键进入模板模式，连续复制相同大小ROI
- **优势**: 快速创建相同大小的ROI，提高效率

### 3. ROI编辑功能
- **位置**: 校准主菜单
- **操作**: `E` 键进入编辑模式，拖拽调整ROI位置
- **优势**: 后期微调，无需重新校准

### 4. 实时阈值保存
- **位置**: 检测模式
- **操作**: `S` 键保存当前调整的阈值
- **优势**: 实时优化检测效果并永久保存

---

## 故障排除

### 常见问题

1. **摄像头无法初始化**
   - 检查摄像头连接
   - 确认摄像头未被其他程序占用

2. **ROI选择无效**
   - 确保ROI尺寸大于最小值(5x5像素)
   - 检查是否在有效区域内

3. **样本采集失败**
   - 确保LED状态正确(全亮/全灭)
   - 检查摄像头图像质量

4. **检测效果不佳**
   - 在检测模式下调整阈值
   - 重新校准ROI位置
   - 优化摄像头参数

### 配置文件恢复
如果配置文件损坏，删除 `combined_config.json` 重新校准即可。

---

## 高级用法和技巧

### 最佳实践

#### LED校准技巧
1. **ROI大小建议**:
   - 包含完整LED区域但避免过大
   - 建议ROI略大于LED实际尺寸的1.2倍

2. **模板复制最佳流程**:
   ```
   选择第一个LED (最标准的) → T进入模板模式 → 快速复制相似LED → Esc退出处理特殊LED
   ```

3. **样本采集注意事项**:
   - 确保光照条件稳定
   - LED状态切换要完全(全亮/全灭)
   - 避免手动遮挡影响采集

#### 检测优化技巧
1. **阈值调整策略**:
   - 先调整灰度阈值(g/G, y/Y)
   - 再微调颜色通道阈值(v/V, r/R)
   - 实时观察检测效果

2. **环境适应**:
   - 光照变化时重新调整阈值
   - 定期保存最优阈值配置

### 快捷键速查表

| 模式 | 按键 | 功能 | 备注 |
|------|------|------|------|
| **摄像头设置** | T/t | 切换分辨率 | 循环切换 |
| | E/e | 调整曝光 | ±1.0 |
| | B/b | 调整亮度 | ±10.0 |
| | S | 保存设置 | |
| | Enter | 进入校准 | |
| **校准主菜单** | L | LED校准 | |
| | D | 数码管校准 | |
| | E | 编辑LEDs | 需要已有ROI |
| | S | 保存退出 | |
| | Enter | 进入检测 | 需要校准完整 |
| **LED ROI选择** | 拖拽 | 选择ROI | |
| | Enter | 确认ROI | |
| | N | 跳过ROI | |
| | B | 回退ROI | ⭐新功能 |
| | T | 模板模式 | ⭐新功能 |
| | R | 重置全部 | |
| | Esc | 返回上级 | |
| **LED编辑** | 1-6 | 选择ROI | ⭐新功能 |
| | 拖拽 | 移动ROI | ⭐新功能 |
| | Enter | 保存更改 | |
| | Esc | 退出编辑 | |
| **检测模式** | g/G | 绿LED灰度阈值 | ±5.0 |
| | v/V | 绿LED绿色阈值 | ±5.0 |
| | y/Y | 红LED灰度阈值 | ±5.0 |
| | r/R | 红LED红色阈值 | ±5.0 |
| | +/- | 数码管亮度阈值 | ±1.0 |
| | S | 保存阈值 | ⭐新功能 |
| | C | 返回校准 | |
| | Q | 退出程序 | |

### 配置文件结构

`combined_config.json` 主要包含：

```json
{
  "camera": {
    "resolution_width": 1920,
    "resolution_height": 1080,
    "exposure": -6.0,
    "brightness": 0.0
  },
  "led": {
    "num_green": 4,
    "num_red": 2,
    "rois": [[x,y,w,h], ...],
    "gray_threshold_green": 160.0,
    "green_threshold": 180.0,
    "gray_threshold_red": 160.0,
    "red_threshold": 100.0
  },
  "digit": {
    "rois": [[x,y,w,h], [x,y,w,h]],
    "segment_rois": [[[x,y,w,h]*7], [[x,y,w,h]*7]],
    "brightness_threshold": 50.0
  }
}
```

---

## 版本信息

**版本**: v1.0
**更新日期**: 2025-01-05
**主要特性**:
- 完整的三阶段工作流程
- LED ROI回退、模板复制、编辑功能
- 实时阈值调整和保存
- 数码管7段显示检测
- 配置文件自动管理

**新增功能**:
- ⭐ LED ROI回退功能 (B键)
- ⭐ LED ROI模板复制功能 (T键)
- ⭐ LED ROI编辑功能 (E键)
- ⭐ 实时阈值保存功能 (S键)

---

*本文档涵盖了系统的完整操作流程和高级用法，建议按顺序阅读并实践操作。如有问题请参考故障排除章节。*
