#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
35点位LED分析测试脚本
用于验证新的配对模式分析和G33/R1/R2监控功能
"""

import subprocess
import re
import sys
import os

def test_analyze_led_log():
    """测试analyze_led_log.py的新功能"""
    print("="*60)
    print("35点位LED分析测试")
    print("="*60)
    
    # 检查日志文件是否存在
    log_file = 'led_digit_detection.log'
    if not os.path.exists(log_file):
        print(f"错误：日志文件 {log_file} 不存在！")
        return False
    
    print(f"日志文件: {log_file}")
    print(f"文件大小: {os.path.getsize(log_file)/1024:.2f} KB")
    
    try:
        # 运行分析脚本
        print("\n正在运行analyze_led_log.py...")
        result = subprocess.run(
            [sys.executable, 'analyze_led_log.py'],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode != 0:
            print(f"脚本执行失败，返回码: {result.returncode}")
            print(f"错误输出: {result.stderr}")
            return False
        
        output = result.stdout
        print("\n" + "="*50)
        print("分析结果:")
        print("="*50)
        
        # 解析关键结果
        cycles_match = re.search(r"Perfect Cycles Found: (\d+)", output)
        g33_match = re.search(r"G33 Duration Analysis: ([\d.]+)s \((\w+)\)", output)
        r1_match = re.search(r"R1 Duration Analysis: ([\d.]+)s \((\w+)\)", output)
        r2_match = re.search(r"R2 Duration Analysis: ([\d.]+)s \((\w+)\)", output)
        special_match = re.search(r"Special LEDs Status: (\w+)", output)
        
        # 显示结果
        if cycles_match:
            cycles = int(cycles_match.group(1))
            print(f"✓ 完美配对周期数: {cycles}")
            print(f"  M10地址发送值: {3 if cycles == 0 else 1}")
        else:
            print("✗ 未找到完美周期数")
            return False
        
        if g33_match:
            print(f"✓ G33分析: {g33_match.group(1)}秒 ({g33_match.group(2)})")
        else:
            print("✗ 未找到G33分析结果")
        
        if r1_match:
            print(f"✓ R1分析: {r1_match.group(1)}秒 ({r1_match.group(2)})")
        else:
            print("✗ 未找到R1分析结果")
        
        if r2_match:
            print(f"✓ R2分析: {r2_match.group(1)}秒 ({r2_match.group(2)})")
        else:
            print("✗ 未找到R2分析结果")
        
        if special_match:
            status = special_match.group(1)
            print(f"✓ 特殊LED综合状态: {status}")
            print(f"  M13地址发送值: {1 if status == 'GOOD' else 3}")
        else:
            print("✗ 未找到特殊LED状态")
        
        # 检查配对模式特征
        print("\n配对模式验证:")
        if "配对序列继续" in output:
            print("✓ 检测到配对序列分析")
        else:
            print("✗ 未检测到配对序列分析")
        
        if "配对验证" in output:
            print("✓ 检测到配对验证逻辑")
        else:
            print("✗ 未检测到配对验证逻辑")
        
        # 显示详细输出（可选）
        show_detail = input("\n是否显示完整输出? (y/N): ").lower().strip()
        if show_detail == 'y':
            print("\n" + "="*50)
            print("完整输出:")
            print("="*50)
            print(output)
        
        return True
        
    except subprocess.TimeoutExpired:
        print("脚本执行超时（60秒）")
        return False
    except Exception as e:
        print(f"执行过程中出错: {e}")
        return False

def test_communication_logic():
    """测试通信逻辑（模拟）"""
    print("\n" + "="*60)
    print("通信逻辑测试（模拟）")
    print("="*60)
    
    # 模拟不同的分析结果
    test_cases = [
        {"cycles": 0, "special": "BAD", "expected_m10": 3, "expected_m13": 3},
        {"cycles": 3, "special": "GOOD", "expected_m10": 1, "expected_m13": 1},
        {"cycles": 5, "special": "BAD", "expected_m10": 1, "expected_m13": 3},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"  完美周期数: {case['cycles']}")
        print(f"  特殊LED状态: {case['special']}")
        print(f"  期望M10发送值: {case['expected_m10']}")
        print(f"  期望M13发送值: {case['expected_m13']}")
        
        # 验证逻辑
        actual_m10 = 3 if case['cycles'] == 0 else 1
        actual_m13 = 1 if case['special'] == 'GOOD' else 3
        
        m10_ok = actual_m10 == case['expected_m10']
        m13_ok = actual_m13 == case['expected_m13']
        
        print(f"  M10逻辑: {'✓' if m10_ok else '✗'} (实际: {actual_m10})")
        print(f"  M13逻辑: {'✓' if m13_ok else '✗'} (实际: {actual_m13})")
    
    return True

def main():
    """主测试函数"""
    print("开始35点位LED分析系统测试...")
    
    # 测试1: 分析脚本功能
    if not test_analyze_led_log():
        print("\n❌ 分析脚本测试失败")
        return False
    
    # 测试2: 通信逻辑
    if not test_communication_logic():
        print("\n❌ 通信逻辑测试失败")
        return False
    
    print("\n" + "="*60)
    print("🎉 所有测试完成！")
    print("="*60)
    print("✓ 配对模式分析正常")
    print("✓ G33/R1/R2监控正常")
    print("✓ 通信逻辑正确")
    print("✓ 系统升级成功")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
